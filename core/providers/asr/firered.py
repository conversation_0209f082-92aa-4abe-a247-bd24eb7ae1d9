# 小红书 FireRedASR 模型的封装

import os
import sys
import io
import wave
from config.logger import setup_logging
from typing import Optional, Tuple, List
import soundfile as sf
import tempfile
import time
import numpy as np
import uuid
from core.providers.asr.base import ASRProviderBase
from core.providers.asr.fireredasr.models.fireredasr import FireRedAsr
from funasr.utils.postprocess_utils import rich_transcription_postprocess

TAG = __name__
logger = setup_logging()

# 捕获标准输出
class CaptureOutput:
    def __enter__(self):
        self._output = io.StringIO()
        self._original_stdout = sys.stdout
        sys.stdout = self._output

    def __exit__(self, exc_type, exc_value, traceback):
        sys.stdout = self._original_stdout
        self.output = self._output.getvalue()
        self._output.close()

        # 将捕获到的内容通过 logger 输出
        if self.output:
            logger.bind(tag=TAG).info(self.output.strip())


class ASRProvider(ASRProviderBase):
    def __init__(self, config: dict, delete_audio_file: bool):
        super().__init__()
        self.model_dir = config.get("model_dir")
        self.output_dir = config.get("output_dir")  # 修正配置键名
        self.asr_type = config.get("asr_type", "aed")  # 支持 "aed" 或 "llm" 模式
        self.delete_audio_file = delete_audio_file
        self.sample_rate = 16000  # FireRedASR 使用 16kHz 采样率

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        with CaptureOutput():
            # 根据系统环境选择合适的设备
            import torch
            
            # FireRedASR设备选择策略：优先CUDA，没有则用CPU（小红书这个模型在Apple Silicon上推理结果不对）
            self.device = "cpu"
            self.use_gpu = 0
            if torch.cuda.is_available():
                self.device = "cuda:0"
                self.use_gpu = 1
                logger.bind(tag=TAG).info(f"FireRedASR uses device: {self.device} for ASR model")
            else:
                logger.bind(tag=TAG).info(f"FireRedASR uses device: {self.device} for ASR model (CUDA not available)")
            
            # 使用配置的 asr_type，保存完整的 FireRedAsr 实例
            begin_time = time.time()
            self.model = FireRedAsr.from_pretrained(self.asr_type, self.model_dir)
            self.model.model.to(self.device)

            # 预热模型
            # 使用项目中的预热音频文件
            warm_up_wav_path = os.path.join("data", "asr_warm_up.wav")
            if not os.path.exists(warm_up_wav_path):
                raise FileNotFoundError(f"预热音频文件不存在: {warm_up_wav_path}")
            
            try:
                results = self.model.transcribe(
                    ["warmup"], 
                    [warm_up_wav_path],
                    {
                        "use_gpu": self.use_gpu,
                        "device": self.device,
                        "beam_size": 1,
                        "nbest": 1,
                        "decode_max_len": 0,
                        "softmax_smoothing": 1.0,
                        "aed_length_penalty": 0.0,
                        "eos_penalty": 1.0,
                        "decode_min_len": 0,
                        "repetition_penalty": 1.0,
                        "llm_length_penalty": 0.0,
                        "temperature": 1.0
                    }
                )
                end_time = time.time()
                
                # 检查识别结果
                recognized_text = results[0]["text"] if results and len(results) > 0 else ""
                expected_text = "晚安"
                
                if recognized_text.strip() != expected_text:
                    error_msg = f"FireRedASR预热失败: 预期识别结果为'{expected_text}'，实际识别结果为'{recognized_text}'"
                    logger.bind(tag=TAG).error(error_msg)
                    raise RuntimeError(error_msg)
                
                logger.bind(tag=TAG).info(f"FireRedASR model warmed up successfully in {end_time - begin_time:.2f} seconds")
                logger.bind(tag=TAG).info(f"预热识别结果验证通过: '{recognized_text}'")
                
            except Exception as e:
                logger.bind(tag=TAG).error(f"FireRedASR预热过程中发生错误: {e}")
                raise

    def save_audio_to_file(self, pcm_data: List[bytes], session_id: str) -> str:
        """PCM数据保存为WAV文件"""
        module_name = __name__.split(".")[-1]
        file_name = f"asr_{module_name}_{session_id}_{uuid.uuid4()}.wav"
        file_path = os.path.join(self.output_dir, file_name)

        with wave.open(file_path, "wb") as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)  # 2 bytes = 16-bit
            wf.setframerate(16000)
            wf.writeframes(b"".join(pcm_data))

        return file_path

    async def speech_to_text(self, opus_data: List[bytes], session_id: str) -> Tuple[Optional[str], Optional[str]]:
        """语音转文本主处理逻辑"""
        file_path = None
        try:
            # 解码 opus 数据为 PCM 数据
            if self.audio_format == "pcm":
                pcm_data = opus_data
            else:
                pcm_data = self.decode_opus(opus_data)

            file_path = self.save_audio_to_file(pcm_data, session_id)
                
            # 创建 uttid
            uttid = f"session_{session_id}"

            # 语音识别
            start_time = time.time()
            results = self.model.transcribe(
                [uttid],
                [file_path],
                {
                    "use_gpu": self.use_gpu,
                    "device": self.device,
                    "beam_size": 1,
                    "nbest": 1,
                    "decode_max_len": 0,
                    "softmax_smoothing": 1.0,
                    "aed_length_penalty": 0.0,
                    "eos_penalty": 1.0,
                    "decode_min_len": 0,
                    "repetition_penalty": 1.0,
                    "llm_length_penalty": 0.0,
                    "temperature": 1.0
                }
            )
            text = results[0]["text"]
            logger.bind(tag=TAG).debug(f"语音识别耗时: {time.time() - start_time:.3f}s | 结果: {text}")
            text = rich_transcription_postprocess(text)
            
            # 根据配置决定是否删除音频文件
            if self.delete_audio_file and file_path and os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                    logger.bind(tag=TAG).debug(f"已删除临时音频文件: {file_path}")
                    file_path = None  # 返回 None 表示文件已删除
                except Exception as e:
                    logger.bind(tag=TAG).error(f"删除音频文件失败: {e}")
                
            return text, file_path
        except Exception as e:
            logger.bind(tag=TAG).error(f"语音识别失败: {e}", exc_info=True)
            # 错误时清理临时文件
            if file_path and os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                    logger.bind(tag=TAG).debug(f"错误时已删除临时音频文件: {file_path}")
                except Exception as cleanup_error:
                    logger.bind(tag=TAG).error(f"清理临时文件失败: {cleanup_error}")
            return None, None


            


