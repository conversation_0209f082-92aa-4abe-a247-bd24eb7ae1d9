import openai
from openai.types import CompletionUsage
from config.logger import setup_logging
from core.utils.util import check_model_key
from core.providers.llm.base import LL<PERSON>roviderBase, is_session_closed

TAG = __name__
logger = setup_logging()


class LLMProvider(LLMProviderBase):
    def __init__(self, config):
        self.model_name = config.get("model_name")
        self.api_key = config.get("api_key")
        if "base_url" in config:
            self.base_url = config.get("base_url")
        else:
            self.base_url = config.get("url")
        max_tokens = config.get("max_tokens")
        if max_tokens is None or max_tokens == "":
            max_tokens = 500

        try:
            max_tokens = int(max_tokens)
        except (ValueError, TypeError):
            max_tokens = 500
        self.max_tokens = max_tokens

        check_model_key("LLM", self.api_key)
        self.client = openai.OpenAI(api_key=self.api_key, base_url=self.base_url)

    def response(self, session_id, dialogue, conn=None):
        responses_stream = None # Renamed from responses to avoid conflict in finally
        try:
            # 处理 Qwen3 模型，在 system prompt 上添加 /no_thinking 指令
            messages = self._process_messages(dialogue)
            
            responses_stream = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                stream=True,
                max_tokens=self.max_tokens,
            )

            # is_active: True means we are outside a <think> block and should yield content.
            # False means we are inside a <think> block and should suppress content.
            is_active = True 
            
            # buffer for text that might be part of a tag split across chunks - not strictly needed with string.find
            # but good for conceptualizing. The current approach processes per-chunk.

            try:
                chunk_count = 0
                for chunk in responses_stream:
                    chunk_count += 1
                    # 每10个chunk检查一次连接状态，减少检查频率
                    if chunk_count % 10 == 0:
                        session_closed = is_session_closed(session_id)
                        conn_closed = conn and getattr(conn, 'is_connection_closed', False)
                        
                        if session_closed or conn_closed:
                            logger.bind(tag=TAG).warning(f"🔌 LLM流式响应中断: session_id={session_id}")
                            logger.bind(tag=TAG).warning(f"🔌 中断原因: session_closed={session_closed}, conn_closed={conn_closed}")
                            logger.bind(tag=TAG).warning(f"🔌 处理进度: 已处理{chunk_count}个chunk")
                            if conn:
                                logger.bind(tag=TAG).warning(f"🔌 连接信息: client_id={getattr(conn, 'client_id', 'unknown')}, device_id={getattr(conn, 'device_id', 'unknown')}")
                            # responses_stream.close() will be handled by finally
                            return
                        
                    try:
                        # Safely access delta and content
                        delta = None
                        if getattr(chunk, "choices", None) and chunk.choices: # Check if choices list exists and is not empty
                           delta = chunk.choices[0].delta
                        
                        current_chunk_text = delta.content if hasattr(delta, "content") and delta.content is not None else ""

                    except IndexError: # Should be less likely with the checks above
                        current_chunk_text = ""
                    
                    if not current_chunk_text and current_chunk_text != "": # Handles None, but allows empty string
                         # If delta.content is None, treat as empty string for processing
                         # But if current_chunk_text is truly empty string, it should still be processed
                         # This case (content is None) implies end of content for the choice or an issue.
                         # Let's ensure current_chunk_text is always a string for find().
                         # The above hasattr and delta.content check should make it a string or None.
                         # The `else ""` ensures it's a string.
                         # So, `if not current_chunk_text:` is sufficient if it's always a string.
                         pass # Allow empty strings to be processed by the loop below, they'll do nothing.


                    # Process the current_chunk_text piece by piece
                    # This loop handles multiple tags or tags mixed with text within a single chunk
                    while current_chunk_text:
                        if is_active:
                            # We are outside a <think> block, looking for its start
                            think_start_pos = current_chunk_text.find("<think>")
                            if think_start_pos != -1:
                                # Found <think>. Yield text before it.
                                text_to_yield = current_chunk_text[:think_start_pos]
                                if text_to_yield:
                                    yield text_to_yield
                                # Advance current_chunk_text past "<think>"
                                current_chunk_text = current_chunk_text[think_start_pos + len("<think>"):]
                                is_active = False # We are now inside a <think> block
                            else:
                                # No <think> tag in the rest of the chunk. Yield all of it.
                                if current_chunk_text:
                                    yield current_chunk_text
                                current_chunk_text = "" # Consumed this part
                        else: # is_active is False, so we are inside a <think> block
                            think_end_pos = current_chunk_text.find("</think>")
                            if think_end_pos != -1:
                                # Found </think>. Discard text up to and including it.
                                # (The text inside the tags is current_chunk_text[:think_end_pos])
                                current_chunk_text = current_chunk_text[think_end_pos + len("</think>"):]
                                is_active = True # We are now outside a <think> block
                            else:
                                # No </think> tag in this chunk. Discard all of it (it's part of thinking).
                                current_chunk_text = "" # Consumed this part
            
            except GeneratorExit:
                # 客户端断开连接，立即关闭流并停止生成
                logger.bind(tag=TAG).info(f"客户端断开连接，立即停止生成 (session_id: {session_id})")
                # responses_stream.close() will be handled by finally
                return # Must return from generator
            except Exception as e:
                logger.bind(tag=TAG).error(f"Error during stream processing: {e}")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in response generation: {e}", exc_info=True)
            # responses_stream.close() will be handled by finally
            # Yield an error message or re-raise
            # yield f"Error: {str(e)}" # Example of yielding an error
            raise # Re-raise the exception
        finally:
            if responses_stream:
                logger.bind(tag=TAG).info(f"Ensuring stream is closed for session_id: {session_id}")
                responses_stream.close()

    def _process_messages(self, dialogue):
        """处理消息，为 Qwen3 模型添加 /no_thinking 指令
        
        Args:
            dialogue: 对话消息列表
            
        Returns:
            处理后的消息列表
        """
        # 如果不是 Qwen3 模型，直接返回原始消息
        if not self.model_name or not "qwen3" in self.model_name.lower():
            return dialogue
        
        # 复制消息列表，避免修改原始数据
        processed_messages = []
        
        for message in dialogue:
            # 复制消息
            new_message = message.copy()
            
            # 如果是系统消息，添加 /no_thinking 指令
            if new_message.get("role") == "system":
                content = new_message.get("content", "")
                # 检查是否已经有 /no_thinking 指令
                if "/no_thinking" not in content:
                    # 在内容末尾添加 /no_thinking
                    new_message["content"] = content + "\n/no_thinking"
            
            processed_messages.append(new_message)
        
        return processed_messages

    def response_with_functions(self, session_id, dialogue, functions=None, conn=None):
        stream = None
        try:
            # 处理 Qwen3 模型，在 system prompt 上添加 /no_thinking 指令
            messages = self._process_messages(dialogue)
            
            stream = self.client.chat.completions.create(
                model=self.model_name, messages=messages, stream=True, tools=functions # tools is the new name for functions
            )

            is_active = True # True: outside <think>, False: inside <think>
            
            try:
                chunk_count = 0
                for chunk in stream:
                    chunk_count += 1
                    # 每10个chunk检查一次连接状态，减少检查频率
                    if chunk_count % 10 == 0:
                        session_closed = is_session_closed(session_id)
                        conn_closed = conn and getattr(conn, 'is_connection_closed', False)
                        
                        if session_closed or conn_closed:
                            logger.bind(tag=TAG).warning(f"🔌 LLM函数调用流式响应中断: session_id={session_id}")
                            logger.bind(tag=TAG).warning(f"🔌 中断原因: session_closed={session_closed}, conn_closed={conn_closed}")
                            logger.bind(tag=TAG).warning(f"🔌 处理进度: 已处理{chunk_count}个chunk")
                            if conn:
                                logger.bind(tag=TAG).warning(f"🔌 连接信息: client_id={getattr(conn, 'client_id', 'unknown')}, device_id={getattr(conn, 'device_id', 'unknown')}")
                            # stream.close() will be handled by finally
                            return
                        
                    # 检查是否存在有效的choice
                    if getattr(chunk, "choices", None) and chunk.choices: # Ensure choices list exists and is not empty
                        # 获取内容
                        delta = chunk.choices[0].delta
                        current_chunk_content = delta.content if hasattr(delta, "content") and delta.content is not None else ""
                        # Get tool_calls for this chunk. These will be yielded alongside any valid text part from this chunk's content.
                        tool_calls_for_this_chunk = delta.tool_calls if hasattr(delta, "tool_calls") else None
                        
                        # Flag to track if we yielded any text from current_chunk_content.
                        # If not, but tool_calls exist and we're active, yield (empty_content, tool_calls)
                        yielded_text_from_current_content = False

                        # Process current_chunk_content piece by piece for <think> tags
                        # This loop handles multiple tags or tags mixed with text within a single chunk's content
                        while current_chunk_content:
                            text_part_to_yield = None # The piece of text determined in this iteration

                            if is_active:
                                # We are outside a <think> block, looking for its start
                                think_start_pos = current_chunk_content.find("<think>")
                                if think_start_pos != -1:
                                    # Found <think>. Text before it is a candidate for yielding.
                                    text_part_to_yield = current_chunk_content[:think_start_pos]
                                    # Advance current_chunk_content past "<think>"
                                    current_chunk_content = current_chunk_content[think_start_pos + len("<think>"):]
                                    is_active = False # We are now inside a <think> block
                                else:
                                    # No <think> tag in the rest of the content. It's all a candidate for yielding.
                                    text_part_to_yield = current_chunk_content
                                    current_chunk_content = "" # Consumed this part
                            else: # is_active is False, so we are inside a <think> block
                                think_end_pos = current_chunk_content.find("</think>")
                                if think_end_pos != -1:
                                    # Found </think>. Discard text up to and including it.
                                    current_chunk_content = current_chunk_content[think_end_pos + len("</think>"):]
                                    is_active = True # We are now outside a <think> block
                                    # No text_part_to_yield from inside the <think> block
                                else:
                                    # No </think> tag in this chunk's content. Discard all of it (it's part of thinking).
                                    current_chunk_content = "" # Consumed this part

                            # Yield if text_part_to_yield is valid (even empty string) OR if there are tool_calls.
                            # The `is_active` check for yielding is implicitly handled by `text_part_to_yield` only being populated
                            # when `is_active` was true at the start of that sub-segment processing.
                            if text_part_to_yield is not None: # It means we processed a segment that could be yielded
                                # Only yield if the text part itself has content OR if there are tool calls for this chunk
                                if text_part_to_yield or tool_calls_for_this_chunk:
                                    yield text_part_to_yield, tool_calls_for_this_chunk
                                yielded_text_from_current_content = True
                        
                        # After processing all of current_chunk_content:
                        # If no text was yielded from this content (e.g., content was initially empty, or all filtered out)
                        # BUT there are tool_calls for this chunk AND we are in an overall active state
                        # (i.e., not suppressed by a <think> tag from a *previous* chunk that hasn't closed)
                        # THEN yield the tool_calls with empty content.
                        if not yielded_text_from_current_content and tool_calls_for_this_chunk and is_active:
                            yield "", tool_calls_for_this_chunk
                            
                    # 存在 CompletionUsage 消息时，生成 Token 消耗 log
                    elif isinstance(getattr(chunk, 'usage', None), CompletionUsage):
                        usage_info = getattr(chunk, 'usage', None)
                        logger.bind(tag=TAG).info(
                            f"Token 消耗：输入 {getattr(usage_info, 'prompt_tokens', '未知')}，" 
                            f"输出 {getattr(usage_info, 'completion_tokens', '未知')}，"
                            f"共计 {getattr(usage_info, 'total_tokens', '未知')}"
                        )
            except GeneratorExit:
                # 客户端断开连接，立即关闭流并停止生成
                logger.bind(tag=TAG).info(f"客户端断开连接，立即停止生成 (session_id: {session_id})")
                # stream.close() will be handled by finally
                return # Must return from generator
            except Exception as e:
                logger.bind(tag=TAG).error(f"Error during stream processing: {e}", exc_info=True)
                yield f"【流处理异常: {e}】", None # Yield error in the expected tuple format

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in function call streaming: {e}", exc_info=True)
            yield f"【抱歉，APUS AI服务发生了故障，我暂时无法对话了，请给我一点时间修复: {e}】", None
        finally:
            if stream:
                logger.bind(tag=TAG).info(f"Ensuring stream is closed for session_id: {session_id} (functions)")
                stream.close()
