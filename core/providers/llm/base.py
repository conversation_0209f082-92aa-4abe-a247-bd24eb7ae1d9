from abc import ABC, abstractmethod
import threading
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

# 全局会话状态字典，用于跟踪会话是否已关闭
_session_states = {}
_session_lock = threading.Lock()

def mark_session_closed(session_id):
    """标记会话为已关闭状态"""
    with _session_lock:
        _session_states[session_id] = True
        logger.bind(tag=TAG).info(f"会话 {session_id} 已标记为关闭状态")

def is_session_closed(session_id):
    """检查会话是否已关闭"""
    with _session_lock:
        return _session_states.get(session_id, False)

def cleanup_session(session_id):
    """清理会话状态"""
    with _session_lock:
        if session_id in _session_states:
            del _session_states[session_id]

class LLMProviderBase(ABC):
    @abstractmethod
    def response(self, session_id, dialogue):
        """LLM response generator"""
        pass

    def response_no_stream(self, system_prompt, user_prompt):
        try:
            # 构造对话格式
            dialogue = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = ""
            for part in self.response("", dialogue):
                result += part
            return result

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in Ollama response generation: {e}")
            return "【LLM服务响应异常】"
    
    def response_with_functions(self, session_id, dialogue, functions=None):
        """
        Default implementation for function calling (streaming)
        This should be overridden by providers that support function calls
        
        Returns: generator that yields either text tokens or a special function call token
        """
        # For providers that don't support functions, just return regular response
        for token in self.response(session_id, dialogue):
            yield token, None

