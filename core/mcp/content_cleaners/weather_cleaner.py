"""
天气工具内容清理器

专门处理天气相关MCP工具返回的内容
"""

from .base_cleaner import BaseMCPCleaner


class WeatherCleaner(BaseMCPCleaner):
    """天气工具内容清理器"""
    
    def __init__(self):
        super().__init__("weather_tool")
    
    def get_max_content_length(self) -> int:
        """天气信息的最大长度阈值"""
        return 500  # 天气信息通常比较简短
    
    def _clean_content_impl(self, content_text: str) -> str:
        """
        清理天气工具返回的内容
        
        清理策略：
        1. 如果长度 < 500，不清理
        2. 保留核心天气信息：温度、天气状况、湿度、风向等
        3. 移除冗余的格式化信息
        
        Args:
            content_text: 预处理后的天气内容
            
        Returns:
            清理后的内容
        """
        # 如果长度不超过阈值，不需要清理
        if not self.should_clean(content_text):
            self.logger.debug(f"{self.tool_name}: 内容长度({len(content_text)})小于阈值，不需要清理")
            return content_text
        
        # 提取核心天气信息
        weather_info = self._extract_weather_info(content_text)
        
        if not weather_info:
            self.logger.warning(f"{self.tool_name}: 未能提取到有效的天气信息")
            return content_text[:500] + "..."
        
        return weather_info
    
    def _extract_weather_info(self, content_text: str) -> str:
        """
        提取核心天气信息
        
        Args:
            content_text: 天气内容
            
        Returns:
            提取的核心天气信息
        """
        import re
        
        lines = content_text.split('\n')
        important_lines = []
        
        # 定义重要的天气信息关键词
        important_keywords = [
            '温度', '天气', '湿度', '风向', '风力', '风速',
            'temperature', 'weather', 'humidity', 'wind',
            '°C', '°F', '%'
        ]
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否包含重要的天气信息
            if any(keyword in line.lower() for keyword in important_keywords):
                important_lines.append(line)
        
        # 如果提取到重要信息，组合返回
        if important_lines:
            result = '\n'.join(important_lines[:10])  # 最多保留10行重要信息
            self.logger.debug(f"{self.tool_name}: 提取到 {len(important_lines)} 行重要天气信息")
            return result
        
        return ""
