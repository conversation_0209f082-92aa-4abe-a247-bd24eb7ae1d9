"""
博查AI搜索内容清理器

专门处理bocha_web_search工具返回的搜索结果内容
"""

from .base_cleaner import BaseMCPCleaner


class BochaSearchCleaner(BaseMCPCleaner):
    """博查AI搜索结果清理器，支持 bocha_web_search 和 bocha_ai_search"""

    # 可配置的长度参数
    MAX_TOTAL_LENGTH = 4000      # 总长度限制
    MAX_SINGLE_RESULT_LENGTH = 1000  # 单条搜索结果长度限制

    def __init__(self, tool_name="bocha_search"):
        super().__init__(tool_name)

    def get_max_content_length(self) -> int:
        """博查搜索结果的最大长度阈值（这里返回0表示总是需要处理）"""
        return 0  # 总是进行格式化处理

    def should_clean(self, content_text: str) -> bool:
        """博查搜索结果总是需要格式化处理"""
        return True  # 总是需要处理
    

    def _clean_content_impl(self, content_text: str) -> str:
        """
        博查搜索结果清理实现
        
        清理策略：
        1. 提取搜索结果中的字段信息
        2. 按新格式组织内容：《标题》（日期/其他字段）：description内容
        3. 控制总长度在限制范围内
        
        Args:
            content_text: 预处理后的搜索结果内容
            
        Returns:
            清理后的内容
        """
        try:
            self.logger.debug(f"🔍 博查搜索清理开始")
            self.logger.debug(f"🔍 输入内容类型: {type(content_text)}")
            self.logger.debug(f"🔍 输入内容是否为None: {content_text is None}")
            
            if content_text is None:
                self.logger.warning(f"🔍 输入内容为None，返回默认消息")
                return "搜索结果为空"
            
            try:
                input_length = len(content_text)
                self.logger.debug(f"🔍 输入内容长度: {input_length}")
                self.logger.debug(f"🔍 输入内容前300字符: {str(content_text)[:300]}...")
            except Exception as len_error:
                self.logger.error(f"🔍 计算输入内容长度时出错: {len_error}")
                self.logger.error(f"🔍 输入内容repr: {repr(content_text)}")
                return "搜索结果格式异常"
            
            # 提取搜索结果字段
            self.logger.debug(f"🔍 开始提取搜索字段")
            search_results = self._extract_search_fields(content_text)
            self.logger.debug(f"🔍 提取到 {len(search_results)} 个搜索结果")
            
            if not search_results:
                self.logger.warning(f"🔍 未提取到任何搜索结果")
                return "未找到有效的搜索结果"
            
            # 构建清理后的内容
            self.logger.debug(f"🔍 开始构建清理后的内容")
            
            # 使用现有的格式化方法
            final_result = self._format_search_results_with_length_control(search_results)
            
            final_length = len(final_result)
            self.logger.debug(f"🔍 最终结果长度: {final_length}")
            self.logger.debug(f"🔍 最终结果前300字符: {final_result[:300]}...")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"🔍 博查搜索清理异常: {e}")
            import traceback
            self.logger.error(f"🔍 异常堆栈: {traceback.format_exc()}")
            # 返回安全的默认值
            if content_text is not None:
                try:
                    return str(content_text)[:1000] + "..."
                except:
                    return "搜索结果处理异常"
            else:
                return "搜索结果为空"
    
    def _extract_search_fields(self, content_text: str) -> list:
        """
        从搜索结果中提取所有字段（改进版）
        
        使用状态机逐行解析，支持多行字段和更健壮的字段识别
        兼容MCP返回的字符串"\n"格式
        
        提取字段：
        - title: 标题（必需）
        - description: 描述（必需）
        - published_date: 发布日期（可选）
        - other_fields: 其他未知字段（可选）
        
        Args:
            content_text: 搜索结果内容
            
        Returns:
            包含各种字段的字典列表
        """
        results = []
        current_result = {}   # 当前结果对象
        current_field = None  # 当前处理的字段
        
        # 已知的核心字段
        core_fields = {'title', 'description'}
        # 已知的日期字段
        date_fields = {'published date', 'published', 'date'}
        # 已知的忽略字段
        ignore_fields = {'url', 'site name'}
        
        # 先将字符串"\n"替换为真正的换行符
        content_text = content_text.replace('\\n', '\n')
        
        for line in content_text.splitlines():
            line = line.strip()
            if not line:
                continue  # 跳过空行
                
            # 检测字段开始（以冒号结尾）
            if ':' in line:
                field_name, field_value = line.split(':', 1)
                field_name = field_name.strip().lower()
                field_value = field_value.strip()
                
                # 检查是否是新结果的开始（Title字段）
                if field_name == 'title':
                    # 保存前一个结果（如果有）
                    if current_result:
                        self._finalize_current_result(current_result, results)
                        current_result = {}
                    
                    current_field = 'title'
                    current_result['title'] = field_value
                    self.logger.debug(f"{self.tool_name}: 提取Title: {field_value}")
                    
                elif field_name == 'description':
                    current_field = 'description'
                    current_result['description'] = field_value
                    self.logger.debug(f"{self.tool_name}: 提取Description: {field_value}")
                    
                elif field_name in date_fields:
                    # 处理日期字段
                    current_field = 'published_date'
                    current_result['published_date'] = field_value
                    self.logger.debug(f"{self.tool_name}: 提取日期字段 {field_name}: {field_value}")
                    
                elif field_name in ignore_fields:
                    # 忽略的字段
                    current_field = None
                    self.logger.debug(f"{self.tool_name}: 跳过忽略字段: {field_name}")
                    
                else:
                    # 其他未知字段，收集到other_fields中
                    current_field = 'other_fields'
                    if 'other_fields' not in current_result:
                        current_result['other_fields'] = []
                    current_result['other_fields'].append(f"{field_name.title()}: {field_value}")
                    self.logger.debug(f"{self.tool_name}: 提取其他字段 {field_name}: {field_value}")
                    
            else:
                # 处理多行字段内容
                if current_field and current_field in ['title', 'description', 'published_date']:
                    # 追加到当前字段（加空格分隔）
                    if current_field in current_result:
                        current_result[current_field] += ' ' + line
                        self.logger.debug(f"{self.tool_name}: 追加到字段 {current_field}: {line[:50]}...")
                    else:
                        # 如果字段不存在，创建它
                        current_result[current_field] = line
                        self.logger.debug(f"{self.tool_name}: 创建字段 {current_field}: {line[:50]}...")
                elif current_field == 'other_fields':
                    # 追加到最后一个other_fields项
                    if current_result.get('other_fields'):
                        current_result['other_fields'][-1] += ' ' + line
                        self.logger.debug(f"{self.tool_name}: 追加到其他字段: {line[:50]}...")
                else:
                    self.logger.debug(f"{self.tool_name}: 跳过无法识别的行: {line[:30]}...")
        
        # 添加最后一个结果
        if current_result:
            self._finalize_current_result(current_result, results)
                
        self.logger.debug(f"{self.tool_name}: 总共提取到 {len(results)} 个有效搜索结果")
        return results

    def _finalize_current_result(self, current_result: dict, results: list):
        """
        完成当前结果的处理并添加到结果列表
        
        Args:
            current_result: 当前结果字典
            results: 结果列表
        """
        if 'title' in current_result:
            # 确保有description字段
            if 'description' not in current_result:
                current_result['description'] = '无描述信息'
                self.logger.debug(f"{self.tool_name}: 添加无描述结果 - Title: {current_result['title'][:30]}...")
            results.append(current_result)
        else:
            self.logger.debug(f"{self.tool_name}: 跳过无标题的结果块")

    def _format_search_results_with_length_control(self, search_results: list) -> str:
        """
        格式化搜索结果并控制长度：《Title》（日期/其他字段）：Description内容

        处理流程：
        1. 先格式化每条搜索结果为：《Title》（日期/其他字段）：Description内容
        2. 如果单条结果长度 > MAX_SINGLE_RESULT_LENGTH 则截断
        3. 逐条添加到最终结果，直到总长度 > MAX_TOTAL_LENGTH 后停止

        Args:
            search_results: 包含各种字段的字典列表

        Returns:
            格式化后的内容
        """
        formatted_lines = []
        current_total_length = 0

        for i, result in enumerate(search_results):
            title = result.get('title', '无标题')
            description = result.get('description', '无描述')
            
            # 构建括号内容（日期和其他字段）
            bracket_content = self._build_bracket_content(result)

            # 步骤1：先格式化为标准格式
            if bracket_content:
                formatted_line = f"《{title}》（{bracket_content}）：{description}"
            else:
                formatted_line = f"《{title}》：{description}"
            
            self.logger.debug(f"{self.tool_name}: 第 {i+1} 个结果格式化后长度: {len(formatted_line)}")

            # 步骤2：如果单条结果超过长度限制，进行截断
            if len(formatted_line) > self.MAX_SINGLE_RESULT_LENGTH:
                formatted_line = self._truncate_single_result(title, description, bracket_content, i+1)

            # 步骤3：检查添加这条结果后是否会超过总长度限制
            separator_length = 2 if formatted_lines else 0  # "\n\n" 的长度
            new_total_length = current_total_length + len(formatted_line) + separator_length

            if new_total_length > self.MAX_TOTAL_LENGTH:
                self.logger.debug(f"{self.tool_name}: 添加第 {i+1} 个结果会超过总长度限制({new_total_length} > {self.MAX_TOTAL_LENGTH})，停止添加")
                break

            # 步骤4：添加到最终结果
            formatted_lines.append(formatted_line)
            current_total_length = new_total_length

            self.logger.debug(f"{self.tool_name}: 成功添加第 {i+1} 个结果，当前总长度: {current_total_length}")

        result_text = '\n\n'.join(formatted_lines)
        self.logger.debug(f"{self.tool_name}: 最终格式化内容长度: {len(result_text)}, 包含 {len(formatted_lines)} 个结果")
        return result_text

    def _build_bracket_content(self, result: dict) -> str:
        """
        构建括号内的内容（日期和其他字段）
        
        Args:
            result: 搜索结果字典
            
        Returns:
            括号内的内容字符串
        """
        bracket_parts = []
        
        # 优先添加日期
        if 'published_date' in result:
            bracket_parts.append(result['published_date'])
        
        # 添加其他字段
        if 'other_fields' in result:
            bracket_parts.extend(result['other_fields'])
        
        return '，'.join(bracket_parts)

    def _truncate_single_result(self, title: str, description: str, bracket_content: str, result_index: int) -> str:
        """
        截断单条搜索结果

        Args:
            title: 标题
            description: 描述
            bracket_content: 括号内容
            result_index: 结果索引（用于日志）

        Returns:
            截断后的格式化结果
        """
        # 构建基础格式
        if bracket_content:
            title_part = f"《{title}》（{bracket_content}）："
        else:
            title_part = f"《{title}》："
            
        available_desc_length = self.MAX_SINGLE_RESULT_LENGTH - len(title_part) - 3  # 3 for "..."

        if available_desc_length > 0:
            # 截断描述内容
            truncated_desc = description[:available_desc_length]
            formatted_line = f"{title_part}{truncated_desc}..."
            self.logger.debug(f"{self.tool_name}: 第 {result_index} 个结果描述过长，截断描述")
        else:
            # 如果标题和括号内容太长，需要截断
            available_title_length = self.MAX_SINGLE_RESULT_LENGTH - 20  # 20 for "《...》（...）：..."
            if available_title_length > 0:
                truncated_title = title[:available_title_length]
                if bracket_content:
                    formatted_line = f"《{truncated_title}...》（{bracket_content[:10]}...）：..."
                else:
                    formatted_line = f"《{truncated_title}...》：..."
                self.logger.debug(f"{self.tool_name}: 第 {result_index} 个结果标题过长，截断标题")
            else:
                formatted_line = "《标题过长》：..."
                self.logger.debug(f"{self.tool_name}: 第 {result_index} 个结果标题极长，使用默认格式")

        self.logger.debug(f"{self.tool_name}: 第 {result_index} 个结果截断后长度: {len(formatted_line)}")
        return formatted_line
