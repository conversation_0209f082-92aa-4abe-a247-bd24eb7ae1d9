"""
MCP内容清理器工厂

负责根据MCP工具名称创建对应的内容清理器
"""

from typing import Dict, Type
from .base_cleaner import BaseMCPCleaner
from .bocha_search_cleaner import BochaSearchCleaner
from .generic_cleaner import GenericMCPCleaner
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class MCPCleanerFactory:
    """MCP内容清理器工厂类"""
    
    # 注册的清理器映射
    _cleaners: Dict[str, Type[BaseMCPCleaner]] = {
        'bocha_web_search': BochaSearchCleaner,
        'bocha_ai_search': BochaSearchCleaner,
        # 可以在这里添加更多专门的清理器
        # 'weather_tool': WeatherCleaner,
        # 'news_tool': NewsCleaner,
    }
    
    @classmethod
    def create_cleaner(cls, tool_name: str) -> BaseMCPCleaner:
        """
        根据工具名称创建对应的清理器
        
        Args:
            tool_name: MCP工具名称
            
        Returns:
            对应的内容清理器实例
        """
        logger.bind(tag=TAG).debug(f"为工具 {tool_name} 创建清理器")
        
        # 检查是否有专门的清理器
        if tool_name in cls._cleaners:
            cleaner_class = cls._cleaners[tool_name]
            # 为BochaSearchCleaner传递工具名称
            if cleaner_class == BochaSearchCleaner:
                cleaner = cleaner_class(tool_name)
            else:
                cleaner = cleaner_class()
            logger.bind(tag=TAG).debug(f"使用专门清理器: {cleaner_class.__name__} for {tool_name}")
            return cleaner
        
        # 使用通用清理器
        cleaner = GenericMCPCleaner(tool_name)
        logger.bind(tag=TAG).debug(f"使用通用清理器: GenericMCPCleaner")
        return cleaner
    
    @classmethod
    def register_cleaner(cls, tool_name: str, cleaner_class: Type[BaseMCPCleaner]):
        """
        注册新的清理器
        
        Args:
            tool_name: MCP工具名称
            cleaner_class: 清理器类
        """
        cls._cleaners[tool_name] = cleaner_class
        logger.bind(tag=TAG).info(f"注册清理器: {tool_name} -> {cleaner_class.__name__}")
    
    @classmethod
    def get_registered_cleaners(cls) -> Dict[str, str]:
        """
        获取已注册的清理器列表
        
        Returns:
            工具名称到清理器类名的映射
        """
        return {tool: cleaner.__name__ for tool, cleaner in cls._cleaners.items()}
    
    @classmethod
    def clean_content(cls, tool_name: str, content_text: str) -> str:
        """
        便捷方法：直接清理内容

        Args:
            tool_name: MCP工具名称
            content_text: 原始内容

        Returns:
            清理后的内容
        """
        try:
            logger.bind(tag=TAG).debug(f"🏭 MCPCleanerFactory.clean_content开始")
            logger.bind(tag=TAG).debug(f"🏭 tool_name: {tool_name}")
            logger.bind(tag=TAG).debug(f"🏭 content_text类型: {type(content_text)}")
            logger.bind(tag=TAG).debug(f"🏭 content_text是否为None: {content_text is None}")
            
            if content_text is not None:
                try:
                    content_length = len(content_text)
                    logger.bind(tag=TAG).debug(f"🏭 content_text长度: {content_length}")
                    logger.bind(tag=TAG).debug(f"🏭 content_text前200字符: {str(content_text)[:200]}...")
                except Exception as len_error:
                    logger.bind(tag=TAG).error(f"🏭 计算content_text长度时出错: {len_error}")
                    logger.bind(tag=TAG).error(f"🏭 content_text的repr: {repr(content_text)}")
                    # 尝试转换为字符串
                    try:
                        content_text = str(content_text)
                        logger.bind(tag=TAG).debug(f"🏭 转换为字符串后长度: {len(content_text)}")
                    except Exception as str_error:
                        logger.bind(tag=TAG).error(f"🏭 转换为字符串失败: {str_error}")
                        return "内容类型转换失败"
            else:
                logger.bind(tag=TAG).warning(f"🏭 content_text为None，返回默认消息")
                return "工具返回空内容"

            logger.bind(tag=TAG).debug(f"🏭 开始创建清理器")
            cleaner = cls.create_cleaner(tool_name)
            logger.bind(tag=TAG).debug(f"🏭 创建的清理器类型: {type(cleaner)}")
            logger.bind(tag=TAG).debug(f"🏭 清理器工具名称: {cleaner.tool_name}")

            logger.bind(tag=TAG).debug(f"🏭 开始调用清理器的clean_content方法")
            result = cleaner.clean_content(content_text)

            logger.bind(tag=TAG).debug(f"🏭 清理器clean_content方法完成")
            logger.bind(tag=TAG).debug(f"🏭 清理结果类型: {type(result)}")
            logger.bind(tag=TAG).debug(f"🏭 清理结果是否为None: {result is None}")
            
            if result is not None:
                try:
                    result_length = len(result)
                    logger.bind(tag=TAG).debug(f"🏭 清理结果长度: {result_length}")
                    logger.bind(tag=TAG).debug(f"🏭 清理结果前200字符: {str(result)[:200]}...")
                except Exception as result_len_error:
                    logger.bind(tag=TAG).error(f"🏭 计算清理结果长度时出错: {result_len_error}")
                    logger.bind(tag=TAG).error(f"🏭 清理结果的repr: {repr(result)}")
                    # 尝试转换为字符串
                    try:
                        result = str(result)
                        logger.bind(tag=TAG).debug(f"🏭 转换清理结果为字符串后长度: {len(result)}")
                    except Exception as result_str_error:
                        logger.bind(tag=TAG).error(f"🏭 转换清理结果为字符串失败: {result_str_error}")
                        return "清理结果类型转换失败"
            else:
                logger.bind(tag=TAG).warning(f"🏭 清理结果为None，返回默认消息")
                return "内容清理后为空"

            return result
        except Exception as e:
            logger.bind(tag=TAG).error(f"🏭 MCPCleanerFactory.clean_content异常: {e}")
            logger.bind(tag=TAG).error(f"🏭 异常类型: {type(e).__name__}")
            import traceback
            logger.bind(tag=TAG).error(f"🏭 异常堆栈: {traceback.format_exc()}")
            # 返回安全的默认值
            if content_text is not None:
                try:
                    return str(content_text)[:2000]  # 截断到安全长度
                except Exception as fallback_error:
                    logger.bind(tag=TAG).error(f"🏭 fallback处理失败: {fallback_error}")
                    return "内容处理完全失败"
            else:
                return "清理器工厂处理失败"
