# MCP内容清理器系统

这个模块提供了一个可扩展的MCP工具内容清理系统，用于处理不同MCP工具返回的内容，避免内容过长导致LLM处理失败。

## 架构设计

### 核心组件

1. **BaseMCPCleaner** - 抽象基类，定义清理器接口
2. **MCPCleanerFactory** - 工厂类，负责创建和管理清理器
3. **具体清理器** - 针对特定MCP工具的清理实现
4. **GenericMCPCleaner** - 通用清理器，处理没有专门清理器的工具

### 设计原则

- **单一职责**：每个清理器只负责一种类型的内容清理
- **开放封闭**：易于扩展新的清理器，无需修改现有代码
- **策略模式**：根据工具类型选择不同的清理策略
- **工厂模式**：统一管理清理器的创建和使用

## 使用方法

### 基本使用

```python
from core.mcp.content_cleaners import MCPCleanerFactory

# 直接清理内容
cleaned_content = MCPCleanerFactory.clean_content("bocha_web_search", raw_content)

# 或者创建清理器实例
cleaner = MCPCleanerFactory.create_cleaner("bocha_web_search")
cleaned_content = cleaner.clean_content(raw_content)
```

### 在connection.py中的集成

```python
def _clean_mcp_content(self, content_text, function_name):
    """清理MCP工具返回的内容，避免过长"""
    from core.mcp.content_cleaners import MCPCleanerFactory
    
    # 使用清理器工厂创建对应的清理器并清理内容
    return MCPCleanerFactory.clean_content(function_name, content_text)
```

## 现有清理器

### 1. BochaSearchCleaner
- **工具名称**: `bocha_web_search`
- **清理策略**: 
  - 长度 < 2000字符时不清理
  - 提取Description字段内容
  - 组合多个描述，限制在4000字符内

### 2. WeatherCleaner (示例)
- **工具名称**: `weather_tool`
- **清理策略**:
  - 长度 < 500字符时不清理
  - 提取核心天气信息（温度、湿度、风向等）
  - 移除冗余格式化信息

### 3. GenericMCPCleaner
- **适用范围**: 所有没有专门清理器的工具
- **清理策略**:
  - 长度 < 1000字符时不清理
  - 移除URL链接
  - 移除重复空行
  - 限制总长度在2000字符内

## 添加新的清理器

### 步骤1：创建清理器类

```python
# core/mcp/content_cleaners/my_tool_cleaner.py
from .base_cleaner import BaseMCPCleaner

class MyToolCleaner(BaseMCPCleaner):
    def __init__(self):
        super().__init__("my_tool_name")
    
    def get_max_content_length(self) -> int:
        return 1500  # 设置阈值
    
    def _clean_content_impl(self, content_text: str) -> str:
        # 实现具体的清理逻辑
        if not self.should_clean(content_text):
            return content_text
        
        # 你的清理逻辑
        cleaned = self._my_cleaning_logic(content_text)
        return cleaned
    
    def _my_cleaning_logic(self, content_text: str) -> str:
        # 具体的清理实现
        pass
```

### 步骤2：注册清理器

```python
# 在cleaner_factory.py中添加
from .my_tool_cleaner import MyToolCleaner

class MCPCleanerFactory:
    _cleaners: Dict[str, Type[BaseMCPCleaner]] = {
        'bocha_web_search': BochaSearchCleaner,
        'my_tool_name': MyToolCleaner,  # 添加这行
        # ...
    }
```

### 步骤3：更新__init__.py

```python
# core/mcp/content_cleaners/__init__.py
from .my_tool_cleaner import MyToolCleaner

__all__ = [
    'BaseMCPCleaner',
    'BochaSearchCleaner',
    'MyToolCleaner',  # 添加这行
    'MCPCleanerFactory'
]
```

## 清理器接口

### 必须实现的方法

```python
def get_max_content_length(self) -> int:
    """返回该工具允许的最大内容长度"""
    pass

def _clean_content_impl(self, content_text: str) -> str:
    """实现具体的清理逻辑"""
    pass
```

### 可选重写的方法

```python
def should_clean(self, content_text: str) -> bool:
    """判断是否需要清理（默认基于长度判断）"""
    return len(content_text) > self.get_max_content_length()

def _preprocess_content(self, content_text: str) -> str:
    """预处理内容（默认移除MCP格式前缀）"""
    pass

def _postprocess_content(self, content_text: str) -> str:
    """后处理内容（默认确保内容不为空）"""
    pass
```

## 最佳实践

1. **明确清理目标**：确定要保留哪些关键信息
2. **设置合理阈值**：根据工具特性设置`get_max_content_length()`
3. **保留核心信息**：优先保留对LLM最有用的信息
4. **添加日志记录**：使用`self.logger`记录清理过程
5. **错误处理**：在清理失败时提供降级方案
6. **测试验证**：确保清理后的内容仍然有用

## 调试和监控

### 日志级别
- **DEBUG**: 详细的清理过程信息
- **INFO**: 清理器创建和注册信息
- **WARNING**: 清理过程中的异常情况
- **ERROR**: 清理失败的错误信息

### 监控指标
- 原始内容长度
- 清理后内容长度
- 清理耗时
- 清理成功率

通过这个系统，可以轻松为不同的MCP工具添加专门的内容清理策略，确保系统的稳定性和响应质量。
