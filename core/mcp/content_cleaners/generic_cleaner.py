"""
通用MCP内容清理器

用于处理没有专门清理器的MCP工具返回的内容
"""

import re
from .base_cleaner import BaseMCPCleaner


class GenericMCPCleaner(BaseMCPCleaner):
    """通用MCP内容清理器"""
    
    def __init__(self, tool_name: str):
        super().__init__(tool_name)
    
    def get_max_content_length(self) -> int:
        """通用清理器的最大长度阈值"""
        return 1000
    
    def _clean_content_impl(self, content_text: str) -> str:
        """
        通用内容清理实现
        
        清理策略：
        1. 移除URL链接
        2. 移除重复的空行
        3. 限制总长度在2000字符以内
        
        Args:
            content_text: 预处理后的内容
            
        Returns:
            清理后的内容
        """
        # 如果内容不长，直接返回
        if not self.should_clean(content_text):
            self.logger.debug(f"{self.tool_name}: 内容长度({len(content_text)})小于阈值，不需要清理")
            return content_text
        
        # 移除URL
        content_text = re.sub(r'https?://[^\s]+', '', content_text)
        self.logger.debug(f"{self.tool_name}: 移除URL后长度: {len(content_text)}")
        
        # 移除重复的空行
        content_text = re.sub(r'\n\s*\n', '\n', content_text)
        self.logger.debug(f"{self.tool_name}: 移除重复空行后长度: {len(content_text)}")
        
        # 限制总长度
        max_length = 2000
        if len(content_text) > max_length:
            content_text = content_text[:max_length] + "..."
            self.logger.debug(f"{self.tool_name}: 截断到 {max_length} 字符")
        
        return content_text.strip()
