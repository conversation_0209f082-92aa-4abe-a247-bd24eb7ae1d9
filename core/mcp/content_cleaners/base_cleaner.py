"""
MCP内容清理基类

定义所有MCP内容清理器的基础接口
"""

from abc import ABC, abstractmethod
from typing import Optional
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class BaseMCPCleaner(ABC):
    """MCP内容清理器基类"""
    
    def __init__(self, tool_name: str):
        """
        初始化清理器
        
        Args:
            tool_name: MCP工具名称
        """
        self.tool_name = tool_name
        self.logger = logger.bind(tag=TAG)
    
    def clean_content(self, content_text: str) -> str:
        """
        清理MCP工具返回的内容
        
        Args:
            content_text: 原始内容文本
            
        Returns:
            清理后的内容文本
        """
        if not content_text:
            self.logger.warning(f"{self.tool_name}: 内容为空")
            return ""
        
        # 记录原始内容信息
        original_length = len(content_text)
        self.logger.debug(f"{self.tool_name}: 开始清理内容，原始长度: {original_length}")
        # 记录完整的原始内容
        self.logger.debug(f"{self.tool_name}: 内容预览: {content_text}")

        # 预处理：移除可能的前缀格式
        processed_content = self._preprocess_content(content_text)

        # 执行具体的清理逻辑
        cleaned_content = self._clean_content_impl(processed_content)

        # 后处理：确保内容不为空
        final_content = self._postprocess_content(cleaned_content)

        # 记录清理结果
        final_length = len(final_content)
        self.logger.debug(f"{self.tool_name}: 清理完成，最终长度: {final_length}")
        # 记录完整的清理后内容
        self.logger.debug(f"清理后内容预览: {final_content}")
        
        return final_content
    
    def _preprocess_content(self, content_text: str) -> str:
        """
        预处理内容，移除通用的前缀格式
        
        Args:
            content_text: 原始内容
            
        Returns:
            预处理后的内容
        """
        # 处理可能的MCP返回格式前缀
        if content_text.startswith("type='text' text='"):
            start_pos = content_text.find("text='") + 6
            end_pos = content_text.rfind("'")
            if end_pos > start_pos:
                content_text = content_text[start_pos:end_pos]
                self.logger.debug(f"{self.tool_name}: 移除了MCP格式前缀")
        
        return content_text
    
    def _postprocess_content(self, content_text: str) -> str:
        """
        后处理内容，确保内容质量
        
        Args:
            content_text: 清理后的内容
            
        Returns:
            最终内容
        """
        if not content_text or len(content_text.strip()) == 0:
            self.logger.warning(f"{self.tool_name}: 清理后内容为空，返回默认提示")
            return f"{self.tool_name}工具没有返回有效内容"
        
        return content_text.strip()
    
    @abstractmethod
    def _clean_content_impl(self, content_text: str) -> str:
        """
        具体的内容清理实现，由子类实现
        
        Args:
            content_text: 预处理后的内容
            
        Returns:
            清理后的内容
        """
        pass
    
    @abstractmethod
    def get_max_content_length(self) -> int:
        """
        获取该工具允许的最大内容长度
        
        Returns:
            最大内容长度
        """
        pass
    
    def should_clean(self, content_text: str) -> bool:
        """
        判断是否需要清理内容
        
        Args:
            content_text: 内容文本
            
        Returns:
            是否需要清理
        """
        return len(content_text) > self.get_max_content_length()
