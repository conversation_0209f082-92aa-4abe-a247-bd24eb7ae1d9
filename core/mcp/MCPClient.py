from __future__ import annotations

import asyncio
import weakref
from typing import Optional, List, Dict, Any

# Assuming 'Client' is the main client class from fastmcp
# and 'Tool' is the model for tool representation if specific typing is needed.
# from fastmcp.models import Tool # Uncomment if Tool model is available and needed
from fastmcp import Client as FastMCPClient
from config.logger import setup_logging

TAG = __name__


class MCPClientContextManager:
    """安全的MCP客户端上下文管理器，确保在同一任务中进入和退出"""

    def __init__(self, client):
        self.client = client
        self._entered = False

    async def __aenter__(self):
        if self.client:
            await self.client.__aenter__()
            self._entered = True
        return self.client

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client and self._entered:
            try:
                await self.client.__aexit__(exc_type, exc_val, exc_tb)
            except RuntimeError as e:
                if "different task" in str(e):
                    # 如果在不同任务中，尝试其他清理方法
                    if hasattr(self.client, 'close'):
                        await self.client.close()
                    elif hasattr(self.client, '_transport') and hasattr(self.client._transport, 'close'):
                        if asyncio.iscoroutinefunction(self.client._transport.close):
                            await self.client._transport.close()
                        else:
                            self.client._transport.close()
                else:
                    raise
            finally:
                self._entered = False


class MCPClient:
    def __init__(self, config: Dict[str, Any]):
        self.logger = setup_logging()
        self.original_config: Dict[str, Any] = config
        # _transform_config may raise ValueError for unsupported configurations (e.g., stdio)
        self.fastmcp_config: Dict[str, Any] = self._transform_config(config)

        self.client: Optional[FastMCPClient] = None
        self.tools: List[Any] = []  # Will store tool objects from FastMCPClient
        self._context_manager = None  # 存储async context manager
        self._is_initialized = False  # 初始化状态标志

        # Determine a service identifier for logging purposes.
        # Assumes the transformed config will have one primary service entry.
        mcp_servers = self.fastmcp_config.get("mcpServers", {})
        if mcp_servers:
            self._service_id_for_logging: str = next(iter(mcp_servers))
        else:
            # This case should ideally not be reached if _transform_config raises errors for invalid/unsupported configs.
            self._service_id_for_logging: str = "unknown_service"

    def _transform_config(self, original_config: Dict[str, Any]) -> Dict[str, Any]:
        if "url" in original_config:
            # Use 'default_mcp_service' or allow service_id to be specified in original_config
            service_id = original_config.get("service_id", "default_mcp_service")

            # 构建服务器配置
            server_config = {
                "url": original_config["url"],
                # Default to 'sse' transport if not specified, or use provided value
                "transport": original_config.get("transport", "sse")
            }

            # 如果原始配置中有headers，则添加到服务器配置中
            if "headers" in original_config:
                server_config["headers"] = original_config["headers"]
                self.logger.bind(tag=TAG).debug(f"MCP配置: {original_config['headers']}")

            return {
                "mcpServers": {
                    service_id: server_config
                }
            }
        elif "command" in original_config:
            self.logger.bind(tag=TAG).error(
                "Stdio configuration ('command') is not supported by this FastMCP-based client. "
                "FastMCPClient primarily supports URL-based transports (e.g., SSE)."
            )
            raise ValueError("Stdio configuration via 'command' is not supported with FastMCPClient.")
        else:
            raise ValueError(
                "MCPClient config must include 'url' for SSE/HTTP-based transport. "
                "'command' (stdio) is not supported."
            )

    async def initialize(self):
        # 检查是否已经初始化
        if self._is_initialized and self.client:
            # 安全检查连接状态
            is_connected = self._is_connected()
            if is_connected:
                self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' already initialized.")
                return

        try:
            # 【调试日志】记录详细的初始化步骤
            self.logger.bind(tag=TAG).info(f"开始初始化MCP客户端 '{self._service_id_for_logging}'")
            self.logger.bind(tag=TAG).debug(f"原始配置: {self.original_config}")
            self.logger.bind(tag=TAG).debug(f"转换后配置: {self.fastmcp_config}")
            
            # 创建新的FastMCP客户端
            self.logger.bind(tag=TAG).debug(f"创建FastMCPClient实例...")
            self.client = FastMCPClient(self.fastmcp_config)
            self.logger.bind(tag=TAG).debug(f"FastMCPClient实例创建成功")

            # 使用安全的context manager进入连接
            self.logger.bind(tag=TAG).debug(f"进入context manager...")
            self._context_manager = MCPClientContextManager(self.client)
            await self._context_manager.__aenter__()
            self.logger.bind(tag=TAG).debug(f"Context manager进入成功")

            # 检查连接状态
            self.logger.bind(tag=TAG).debug(f"检查连接状态...")
            is_connected = self._is_connected()
            self.logger.bind(tag=TAG).debug(f"连接状态: {is_connected}")
            
            if not is_connected:
                self.logger.bind(tag=TAG).error(f"MCPClient failed to connect to service '{self._service_id_for_logging}'.")
                await self._safe_cleanup()
                raise ConnectionError(f"Failed to connect to MCP server '{self._service_id_for_logging}'.")

            # 获取工具列表
            self.logger.bind(tag=TAG).debug(f"获取工具列表...")
            raw_tools = await self.client.list_tools()
            self.tools = raw_tools if raw_tools else []
            self.logger.bind(tag=TAG).debug(f"获取到 {len(self.tools)} 个工具")

            self._is_initialized = True
            self.logger.bind(tag=TAG).info(
                f"Connected via FastMCP to service '{self._service_id_for_logging}', tools = {[t.name for t in self.tools]}"
            )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error during MCPClient initialization for service '{self._service_id_for_logging}': {e}")
            self.logger.bind(tag=TAG).error(f"异常类型: {type(e).__name__}")
            self.logger.bind(tag=TAG).error(f"异常详情: {str(e)}", exc_info=True)
            await self._safe_cleanup()
            raise  # Re-raise the original initialization exception

    async def cleanup(self):
        """清理MCP客户端连接"""
        if not self._is_initialized:
            self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' already cleaned up or not initialized.")
            return

        self.logger.bind(tag=TAG).info(f"Cleaning up MCPClient for service '{self._service_id_for_logging}'...")
        await self._safe_cleanup()

    async def _safe_cleanup(self):
        """安全清理方法，处理各种异常情况"""
        try:
            if self._context_manager:
                # 尝试正常退出context manager
                try:
                    await self._context_manager.__aexit__(None, None, None)
                    self.logger.bind(tag=TAG).debug(f"MCPClient context manager exited for '{self._service_id_for_logging}'")
                except RuntimeError as e:
                    if "different task" in str(e):
                        self.logger.bind(tag=TAG).warning(f"Context manager exit in different task - attempting alternative cleanup")
                        await self._alternative_cleanup()
                    else:
                        raise
                except Exception as e:
                    self.logger.bind(tag=TAG).warning(f"Error exiting context manager: {e}")
                    await self._alternative_cleanup()
            elif self.client:
                await self._alternative_cleanup()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error during MCPClient cleanup for '{self._service_id_for_logging}': {e}")
        finally:
            # 重置所有状态
            self.client = None
            self.tools = []
            self._context_manager = None
            self._is_initialized = False
            self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' cleaned up.")

    async def _alternative_cleanup(self):
        """替代清理方法，当正常context manager退出失败时使用"""
        if not self.client:
            return

        try:
            # 尝试close方法
            if hasattr(self.client, 'close'):
                await self.client.close()
                self.logger.bind(tag=TAG).debug(f"MCPClient close() called for '{self._service_id_for_logging}'")
                return

            # 尝试disconnect方法
            if hasattr(self.client, 'disconnect'):
                await self.client.disconnect()
                self.logger.bind(tag=TAG).debug(f"MCPClient disconnect() called for '{self._service_id_for_logging}'")
                return

            # 尝试关闭transport
            if hasattr(self.client, '_transport') and hasattr(self.client._transport, 'close'):
                if asyncio.iscoroutinefunction(self.client._transport.close):
                    await self.client._transport.close()
                else:
                    self.client._transport.close()
                self.logger.bind(tag=TAG).debug(f"MCPClient transport closed for '{self._service_id_for_logging}'")
                return

            self.logger.bind(tag=TAG).debug(f"No alternative cleanup method available, client will be garbage collected")

        except Exception as e:
            self.logger.bind(tag=TAG).warning(f"Alternative cleanup failed: {e}")

    def _is_connected(self) -> bool:
        """安全检查连接状态"""
        self.logger.bind(tag=TAG).debug(f"检查连接状态详情...")
        self.logger.bind(tag=TAG).debug(f"client存在: {self.client is not None}")
        self.logger.bind(tag=TAG).debug(f"_is_initialized: {self._is_initialized}")
        
        if not self.client:
            self.logger.bind(tag=TAG).debug(f"client不存在，返回False")
            return False
            
        # 注意：在初始化过程中_is_initialized还是False，所以不能依赖它
        try:
            if hasattr(self.client, 'is_connected'):
                self.logger.bind(tag=TAG).debug(f"client有is_connected方法")
                if callable(self.client.is_connected):
                    result = self.client.is_connected()
                    self.logger.bind(tag=TAG).debug(f"is_connected()返回: {result}")
                    return result
                else:
                    result = self.client.is_connected
                    self.logger.bind(tag=TAG).debug(f"is_connected属性值: {result}")
                    return result
            else:
                self.logger.bind(tag=TAG).debug(f"client没有is_connected方法/属性")
                # 检查其他可能的连接状态指示器
                if hasattr(self.client, '_transport'):
                    self.logger.bind(tag=TAG).debug(f"检查transport状态")
                    transport = self.client._transport
                    if transport:
                        self.logger.bind(tag=TAG).debug(f"transport存在: {type(transport)}")
                        # 对于SSE transport，可能有不同的状态检查方法
                        return True  # 假设transport存在就是连接的
                    else:
                        self.logger.bind(tag=TAG).debug(f"transport不存在")
                        return False
                        
        except Exception as e:
            self.logger.bind(tag=TAG).debug(f"检查连接状态时异常: {e}")
            return False
            
        # 如果没有明确的连接状态方法，在context manager成功进入后假设已连接
        self.logger.bind(tag=TAG).debug(f"没有明确的连接状态检查方法，假设已连接")
        return True

    def has_tool(self, name: str) -> bool:
        if not self._is_connected():
            return False
        return any(tool.name == name for tool in self.tools)

    def get_available_tools(self) -> List[Dict[str, Any]]:
        if not self._is_connected():
            return []
        
        tool_list = []
        for t in self.tools:
            tool_info = {
                "type": "function",
                "function": {
                    "name": getattr(t, 'name', 'Unknown Tool'),
                    "description": getattr(t, 'description', ''),
                    "parameters": getattr(t, 'input_schema', {}),
                },
            }
            tool_list.append(tool_info)
        return tool_list

    async def call_tool(self, name: str, args: dict) -> Any:
        if not self._is_connected():
            self.logger.bind(tag=TAG).error(f"MCPClient for service '{self._service_id_for_logging}' not initialized or connected.")
            raise RuntimeError(f"MCPClient for service '{self._service_id_for_logging}' not initialized or connected.")

        try:
            # Call the tool with positional arguments as expected by FastMCPClient
            result = await self.client.call_tool(name, args)
            # Wrap the result in the expected format with content attribute
            from dataclasses import dataclass
            
            @dataclass
            class TextContent:
                type: str = "text"
                text: str = ""
                annotations: Any = None
                
            # If result is a list, join it with newlines
            if isinstance(result, list):
                text_content = "\n".join(str(item) for item in result)
            else:
                text_content = str(result)
                
            return type('ToolResult', (), {
                'content': [TextContent(text=text_content)],
                'isError': False
            })
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error calling tool '{name}' on service '{self._service_id_for_logging}' with args {args}: {e}")
            raise # Re-raise the error, allowing callers to handle it
