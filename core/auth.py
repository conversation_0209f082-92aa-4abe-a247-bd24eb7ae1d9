from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class AuthenticationError(Exception):
    """认证异常"""
    pass


class AuthMiddleware:
    def __init__(self, config):
        self.config = config
        self.auth_config = config["server"].get("auth", {})
        # 构建token查找表
        self.tokens = {
            item["token"]: item["name"]
            for item in self.auth_config.get("tokens", [])
        }
        # 设备白名单
        self.allowed_devices = set(
            self.auth_config.get("allowed_devices", [])
        )

    async def authenticate(self, headers):
        """验证连接请求"""
        device_id = headers.get("device-id", "")
        client_id = headers.get("client-id", "")

        # 检查是否启用认证
        if not self.auth_config.get("enabled", False):
            logger.bind(tag=TAG).info(f"Skip Authentication - Device: {device_id}")
            return True

        # 检查设备是否在白名单中
        if self.allowed_devices and device_id in self.allowed_devices:
            logger.bind(tag=TAG).info(f"Device in whitelist - Device: {device_id}")
            return True

        # 验证Authorization header
        auth_header = headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            logger.bind(tag=TAG).error("Missing or invalid Authorization header")
            raise AuthenticationError("Missing or invalid Authorization header")

        token = auth_header.split(" ")[1]
        
        # 根据认证类型进行不同的验证
        auth_type = self.auth_config.get("auth_type", "static")
        
        if auth_type == "static":
            # 静态token验证
            if token not in self.tokens:
                logger.bind(tag=TAG).error(f"Invalid token: {token}")
                raise AuthenticationError("Invalid token")
            
            logger.bind(tag=TAG).info(f"Static authentication successful - Device: {device_id}, Token: {self.tokens[token]}")
            return True
            
        elif auth_type == "account":
            # APUS账号系统认证
            # 这部分代码暂未实现，先返回 true
            logger.bind(tag=TAG).info(f"Account authentication - Device: {device_id}, Client: {client_id}")
            return True
            
        else:
            # 未知的认证类型
            logger.bind(tag=TAG).error(f"Unknown authentication type: {auth_type}")
            raise AuthenticationError(f"Unknown authentication type: {auth_type}")
        
        return True

    def get_token_name(self, token):
        """获取token对应的设备名称"""
        return self.tokens.get(token)
