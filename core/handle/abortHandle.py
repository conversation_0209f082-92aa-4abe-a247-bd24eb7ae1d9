import json
import queue
from config.logger import setup_logging

TAG = __name__


async def handleAbortMessage(conn):
    conn.logger.bind(tag=TAG).info("Abort message received")
    # 设置成打断状态，会自动打断llm、tts任务
    conn.client_abort = True
    conn.clear_queues(send_poison_pill=False)  # 手动abort也不应该终止线程，只清空队列
    # 打断客户端说话状态
    await conn.websocket.send(
        json.dumps({"type": "tts", "state": "stop", "session_id": conn.session_id})
    )
    conn.clearSpeakStatus()

    # 【重要修复】短暂延迟后重置中断标志，允许后续对话
    # abort应该只是临时中断当前任务，而不是永久禁用对话功能
    import asyncio
    async def reset_abort_flag():
        await asyncio.sleep(0.5)  # 等待500ms确保当前任务被清理
        conn.client_abort = False
        conn.logger.bind(tag=TAG).debug("🔄 Abort处理完成，重置client_abort标志")

    # 异步执行重置，不阻塞当前处理
    asyncio.create_task(reset_abort_flag())

    conn.logger.bind(tag=TAG).info("Abort message received-end")
