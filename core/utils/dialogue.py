import uuid
import copy
import asyncio
from typing import List, Dict
from datetime import datetime
from core.utils.util import get_string_no_punctuation_or_emoji


class Message:
    def __init__(
        self,
        role: str,
        content: str = None,
        uniq_id: str = None,
        tool_calls=None,
        tool_call_id=None,
    ):
        self.uniq_id = uniq_id if uniq_id is not None else str(uuid.uuid4())
        self.role = role
        self.content = content
        self.tool_calls = tool_calls
        self.tool_call_id = tool_call_id


class Dialogue:
    def __init__(self, max_context_tokens=4000):
        self.dialogue: List[Message] = []
        # 获取当前时间
        self.current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 最大上下文token数量
        self.max_context_tokens = max_context_tokens

    def put(self, message: Message):
        self.dialogue.append(message)

    def _estimate_tokens(self, text: str) -> int:
        """简单估算文本的token数量（中文按字符数，英文按单词数）"""
        if not text:
            return 0
        
        # 简单估算：中文字符*1.5 + 英文单词*1.2
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len([w for w in text.split() if w.isalpha()])
        other_chars = len(text) - chinese_chars
        
        return int(chinese_chars * 1.5 + english_words * 1.2 + other_chars * 0.5)

    def _calculate_dialogue_tokens(self, messages: List[Message]) -> int:
        """计算对话消息的总token数"""
        total_tokens = 0
        for msg in messages:
            if msg.content:
                total_tokens += self._estimate_tokens(msg.content)
            # 为tool_calls等额外内容预留token
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                total_tokens += 100  # 预估tool_calls的token消耗
        return total_tokens

    def getMessages(self, m, dialogue):
        if m.tool_calls is not None:
            dialogue.append({"role": m.role, "tool_calls": m.tool_calls})
        elif m.role == "tool":
            dialogue.append(
                {
                    "role": m.role,
                    "tool_call_id": (
                        str(uuid.uuid4()) if m.tool_call_id is None else m.tool_call_id
                    ),
                    "content": m.content,
                }
            )
        else:
            dialogue.append({"role": m.role, "content": m.content})

    def get_llm_dialogue(self) -> List[Dict[str, str]]:
        dialogue = []
        for m in self.dialogue:
            self.getMessages(m, dialogue)
        return dialogue

    def update_system_message(self, new_content: str):
        """更新或添加系统消息"""
        # 查找第一个系统消息
        system_msg = next((msg for msg in self.dialogue if msg.role == "system"), None)
        if system_msg:
            system_msg.content = new_content
        else:
            self.put(Message(role="system", content=new_content))

    def get_llm_dialogue_with_limit(self, max_tokens=4000) -> List[Dict[str, str]]:
        """获取限制token数量的对话历史"""
        dialogue = []
        estimated_tokens = 0
        
        # 首先添加system消息（总是保留）
        system_messages = [msg for msg in self.dialogue if msg.role == "system"]
        for msg in system_messages:
            self.getMessages(msg, dialogue)
            estimated_tokens += self._estimate_tokens(msg.content or "")
        
        # 从最新的消息开始倒序添加
        other_messages = [msg for msg in self.dialogue if msg.role != "system"]
        temp_dialogue = []
        
        for msg in reversed(other_messages):
            msg_tokens = self._estimate_tokens(msg.content or "")
            if estimated_tokens + msg_tokens > max_tokens:
                break
            temp_dialogue.insert(0, msg)
            estimated_tokens += msg_tokens
        
        # 添加到最终对话中
        for msg in temp_dialogue:
            self.getMessages(msg, dialogue)
        
        return dialogue
    
    def get_llm_dialogue_with_memory(
        self, memory_str: str = None, max_tokens: int = None
    ) -> List[Dict[str, str]]:
        """获取包含记忆信息的对话历史"""
        if max_tokens:
            # 使用token限制的方法
            dialogue = self.get_llm_dialogue_with_limit(max_tokens)
        else:
            # 使用原有方法
            dialogue = []
            for m in self.dialogue:
                self.getMessages(m, dialogue)

        # 构建增强的上下文
        enhanced_dialogue = []

        # 处理system消息，整合记忆
        system_message = next(
            (msg for msg in dialogue if msg.get("role") == "system"), None
        )
        
        if system_message:
            enhanced_content = system_message['content']
            
            # 添加记忆信息（包含用户记忆和对话历史总结）
            if memory_str is not None and len(str(memory_str)) > 0:
                enhanced_content += f"\n\n{memory_str}"

            enhanced_dialogue.append({"role": "system", "content": enhanced_content})
        elif memory_str is not None and len(str(memory_str)) > 0:
            # 如果没有system消息但有记忆，创建一个
            enhanced_dialogue.append({
                "role": "system", 
                "content": memory_str
            })

        # 添加用户和助手的对话（跳过system消息）
        for msg in dialogue:
            if msg.get("role") != "system":
                enhanced_dialogue.append(msg)

        return enhanced_dialogue

    def get_llm_dialogue_with_smart_limit(
        self, memory_summary: str = "", max_context_tokens: int = 4000, max_total_tokens: int = 524288
    ) -> List[Dict[str, str]]:
        """
        智能token分配：优先保证用户对话上下文，然后尽可能包含工具调用结果

        Args:
            memory_summary: 记忆摘要字符串
            max_context_tokens: 用户对话上下文的最大token数
            max_total_tokens: 总的最大token数

        Returns:
            优化后的对话历史
        """
        # 分离不同类型的消息，保持时间顺序
        system_messages = []
        user_assistant_messages = []
        tool_related_messages = []  # 包含tool_calls和tool response的消息

        for msg in self.dialogue:
            if msg.role == "system":
                system_messages.append(msg)
            elif msg.role in ["user", "assistant"]:
                # 包含tool_calls的assistant消息属于工具调用链
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_related_messages.append(msg)
                else:
                    user_assistant_messages.append(msg)
            elif msg.role == "tool":
                tool_related_messages.append(msg)

        # 构建基础对话（system + 用户对话上下文）
        base_dialogue = []
        base_tokens = 0

        # 添加system消息
        for msg in system_messages:
            self.getMessages(msg, base_dialogue)
            base_tokens += self._estimate_tokens(msg.content or "")

        # 添加记忆摘要到system消息
        if memory_summary:
            # 将摘要合并到最后一个system消息中
            if base_dialogue and base_dialogue[-1]["role"] == "system":
                base_dialogue[-1]["content"] += f"\n\n历史对话总结：{memory_summary}"
            else:
                # 如果没有system消息，创建一个
                base_dialogue.append({"role": "system", "content": f"历史对话总结：{memory_summary}"})
            base_tokens += self._estimate_tokens(memory_summary)

        # 从最新的用户对话开始，逐个添加直到达到上下文限制
        temp_user_messages = []
        for msg in reversed(user_assistant_messages):
            msg_tokens = self._estimate_tokens(msg.content or "")
            if base_tokens + msg_tokens > max_context_tokens:
                break
            temp_user_messages.insert(0, msg)
            base_tokens += msg_tokens

        # 添加用户对话到基础对话
        for msg in temp_user_messages:
            self.getMessages(msg, base_dialogue)

        # 检查是否有未完成的工具调用链
        current_tokens = base_tokens
        final_dialogue = base_dialogue.copy()
        
        # 检查最新的用户消息后是否有未完成的工具调用
        if temp_user_messages:
            last_user_msg = temp_user_messages[-1]
            last_user_index = self.dialogue.index(last_user_msg)
            
            # 查找该用户消息之后的工具相关消息
            tool_chain_after_last_user = []
            incomplete_tool_chain = False
            
            for i in range(last_user_index + 1, len(self.dialogue)):
                msg = self.dialogue[i]
                if msg in tool_related_messages:
                    tool_chain_after_last_user.append(msg)
                    # 检查是否是未完成的工具调用（有tool_calls但没有对应的tool response）
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        # 检查后续是否有对应的tool response
                        tool_call_ids = [call.get('id') if hasattr(call, 'get') else call['id'] for call in msg.tool_calls]
                        has_response = False
                        for j in range(i + 1, len(self.dialogue)):
                            next_msg = self.dialogue[j]
                            if (next_msg.role == "tool" and 
                                hasattr(next_msg, 'tool_call_id') and 
                                next_msg.tool_call_id in tool_call_ids):
                                has_response = True
                                break
                        if not has_response:
                            incomplete_tool_chain = True
                            break
                elif msg.role in ["user", "assistant"]:
                    # 遇到新的用户或助手消息，工具调用链结束
                    break
            
            # 如果有未完成的工具调用链，清除它以避免混乱
            if incomplete_tool_chain:
                # 只保留完整的工具调用对（tool_calls + tool response）
                complete_tool_pairs = []
                i = 0
                while i < len(tool_chain_after_last_user):
                    msg = tool_chain_after_last_user[i]
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        # 查找对应的tool response
                        tool_call_ids = [call.get('id') if hasattr(call, 'get') else call['id'] for call in msg.tool_calls]
                        found_response = False
                        for j in range(i + 1, len(tool_chain_after_last_user)):
                            next_msg = tool_chain_after_last_user[j]
                            if (next_msg.role == "tool" and 
                                hasattr(next_msg, 'tool_call_id') and 
                                next_msg.tool_call_id in tool_call_ids):
                                # 找到完整的工具调用对
                                complete_tool_pairs.extend([msg, next_msg])
                                found_response = True
                                i = j + 1
                                break
                        if not found_response:
                            # 未完成的工具调用，跳过
                            break
                    else:
                        i += 1
                
                tool_chain_after_last_user = complete_tool_pairs

            # 尝试添加完整的工具调用链
            for msg in tool_chain_after_last_user:
                msg_tokens = self._estimate_tokens(msg.content or "")
                if current_tokens + msg_tokens > max_total_tokens:
                    break
                
                tool_dialogue_entry = []
                self.getMessages(msg, tool_dialogue_entry)
                final_dialogue.extend(tool_dialogue_entry)
                current_tokens += msg_tokens

        return final_dialogue

    def get_llm_dialogue_with_memory_summary(
        self, memory_summary: str = "",
        max_context_tokens: int = 4000, max_total_tokens: int = 524288
    ) -> List[Dict[str, str]]:
        """
        智能token分配：优先保证用户对话上下文，然后尽可能包含工具调用结果
        将历史总结合并到system prompt中

        Args:
            memory_summary: 历史对话总结
            max_context_tokens: 用户对话上下文的最大token数
            max_total_tokens: 总的最大token数

        Returns:
            优化后的对话历史
        """
        # 分离不同类型的消息，保持时间顺序
        system_messages = []
        user_assistant_messages = []
        tool_related_messages = []  # 包含tool_calls和tool response的消息

        for msg in self.dialogue:
            if msg.role == "system":
                system_messages.append(msg)
            elif msg.role in ["user", "assistant"]:
                # 包含tool_calls的assistant消息属于工具调用链
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_related_messages.append(msg)
                else:
                    user_assistant_messages.append(msg)
            elif msg.role == "tool":
                tool_related_messages.append(msg)

        # 构建基础对话（system + 用户对话上下文）
        base_dialogue = []
        base_tokens = 0

        # 添加system消息，并将历史总结合并到system prompt中
        for msg in system_messages:
            system_content = msg.content or ""

            # 如果有历史总结，将其合并到system prompt中
            if memory_summary:
                system_content += f"历史对话总结：{memory_summary}"

            base_dialogue.append({"role": "system", "content": system_content})
            base_tokens += self._estimate_tokens(system_content)

        # 重构后不再添加历史消息，只使用摘要

        # 从最新的用户对话开始，逐个添加直到达到上下文限制
        temp_user_messages = []
        for msg in reversed(user_assistant_messages):
            msg_tokens = self._estimate_tokens(msg.content or "")
            if base_tokens + msg_tokens > max_context_tokens:
                break
            temp_user_messages.insert(0, msg)
            base_tokens += msg_tokens

        # 添加用户对话到基础对话
        for msg in temp_user_messages:
            self.getMessages(msg, base_dialogue)

        # 检查是否有未完成的工具调用链
        current_tokens = base_tokens
        final_dialogue = base_dialogue.copy()

        # 检查最新的用户消息后是否有未完成的工具调用
        if temp_user_messages:
            last_user_msg = temp_user_messages[-1]
            last_user_index = self.dialogue.index(last_user_msg)

            # 查找该用户消息之后的工具相关消息
            tool_chain_after_last_user = []
            incomplete_tool_chain = False

            for i in range(last_user_index + 1, len(self.dialogue)):
                msg = self.dialogue[i]
                if msg in tool_related_messages:
                    tool_chain_after_last_user.append(msg)
                    # 检查是否是未完成的工具调用（有tool_calls但没有对应的tool response）
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        # 检查后续是否有对应的tool response
                        tool_call_ids = [call.get('id') if hasattr(call, 'get') else call['id'] for call in msg.tool_calls]
                        has_response = False
                        for j in range(i + 1, len(self.dialogue)):
                            next_msg = self.dialogue[j]
                            if (next_msg.role == "tool" and
                                hasattr(next_msg, 'tool_call_id') and
                                next_msg.tool_call_id in tool_call_ids):
                                has_response = True
                                break
                        if not has_response:
                            incomplete_tool_chain = True
                            break
                elif msg.role in ["user", "assistant"]:
                    # 遇到新的用户或助手消息，工具调用链结束
                    break

            # 如果有未完成的工具调用链，清除它以避免混乱
            if incomplete_tool_chain:
                # 只保留完整的工具调用对（tool_calls + tool response）
                complete_tool_pairs = []
                i = 0
                while i < len(tool_chain_after_last_user):
                    msg = tool_chain_after_last_user[i]
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        # 查找对应的tool response
                        tool_call_ids = [call.get('id') if hasattr(call, 'get') else call['id'] for call in msg.tool_calls]
                        found_response = False
                        for j in range(i + 1, len(tool_chain_after_last_user)):
                            next_msg = tool_chain_after_last_user[j]
                            if (next_msg.role == "tool" and
                                hasattr(next_msg, 'tool_call_id') and
                                next_msg.tool_call_id in tool_call_ids):
                                # 找到完整的工具调用对
                                complete_tool_pairs.extend([msg, next_msg])
                                found_response = True
                                i = j + 1
                                break
                        if not found_response:
                            # 未完成的工具调用，跳过
                            break
                    else:
                        i += 1

                tool_chain_after_last_user = complete_tool_pairs

            # 尝试添加完整的工具调用链
            for msg in tool_chain_after_last_user:
                msg_tokens = self._estimate_tokens(msg.content or "")
                if current_tokens + msg_tokens > max_total_tokens:
                    break

                tool_dialogue_entry = []
                self.getMessages(msg, tool_dialogue_entry)
                final_dialogue.extend(tool_dialogue_entry)
                current_tokens += msg_tokens

        return final_dialogue

    async def compress_context_with_memory(self, memory, fc_max_tokens: int, policy_check=None):
        """
        当上下文超过fc_max_tokens时，智能压缩对话历史
        
        逻辑：
        1. 从最新的消息开始往前倒推，找到超过fc_max_tokens的分界点
        2. 保持用户-助手对话的完整性
        3. 将超出部分通过memory的save_memory保存
        4. 用memory的query_memory获取历史摘要
        5. 返回摘要+保留的对话
        
        Args:
            memory: 内存管理器实例
            fc_max_tokens: 最大token限制
            
        Returns:
            压缩后的对话列表
        """
        if not self.dialogue:
            return []
            
        # 计算当前对话的总token数
        current_tokens = self._calculate_dialogue_tokens(self.dialogue)
        
        # 无论是否超过限制，都尝试加载历史记忆摘要
        try:
            memory_summary = await memory.query_memory("")
            
            # 对从Redis获取的历史记忆进行内容安全检测（源头检测，统一处理）
            if policy_check and memory_summary:
                try:
                    # 检测历史记忆内容是否违规
                    check_result = policy_check.check_answer("总结聊天记录", memory_summary)
                    from config.logger import setup_logging
                    logger = setup_logging()
                    logger.bind(tag="dialogue").info(f"Policy check result: {check_result}")
                    if check_result.get("is_blocked", False):
                        logger.bind(tag="dialogue").warning(f"Memory content blocked by policy check: {check_result.get('detect_result')}")
                        memory_summary = ""  # 在源头丢弃被拦截的历史记忆
                except Exception as policy_error:
                    from config.logger import setup_logging
                    logger = setup_logging()
                    logger.bind(tag="dialogue").error(f"Policy check failed for memory: {policy_error}")
                    # 检测失败不影响正常流程，继续使用原memory_summary
                    
        except Exception as e:
            memory_summary = ""
            from config.logger import setup_logging
            logger = setup_logging()
            logger.bind(tag="dialogue").error(f"Memory query failed: {e}")

        # 重构后只返回摘要字符串，不再返回原始对话
        memory_messages = []  # 不再使用历史消息列表

        if current_tokens <= fc_max_tokens:
            # 没有超过限制，返回原对话+历史记忆
            final_dialogue = []

            # 添加system消息，并将历史总结合并到system prompt中
            system_messages = [msg for msg in self.dialogue if msg.role == "system"]
            for msg in system_messages:
                system_content = msg.content or ""

                # 如果有历史总结，将其合并到system prompt中
                if memory_summary:
                    system_content += f"历史对话总结：{memory_summary}"

                final_dialogue.append({"role": "system", "content": system_content})

            # 重构后不再添加历史消息，只使用摘要
            
            # 添加当前对话中的非system消息
            for msg in self.dialogue:
                if msg.role != "system":
                    if msg.tool_calls is not None:
                        final_dialogue.append({"role": msg.role, "tool_calls": msg.tool_calls})
                    elif msg.role == "tool":
                        final_dialogue.append({
                            "role": msg.role,
                            "content": msg.content or "",
                            "tool_call_id": msg.tool_call_id
                        })
                    else:
                        final_dialogue.append({"role": msg.role, "content": msg.content or ""})
            
            return final_dialogue
        
        # 分离system消息和其他消息
        system_messages = [msg for msg in self.dialogue if msg.role == "system"]
        other_messages = [msg for msg in self.dialogue if msg.role != "system"]
        
        if not other_messages:
            return self.get_llm_dialogue()
        
        # 计算system消息的token数
        system_tokens = sum(self._estimate_tokens(msg.content or "") for msg in system_messages)
        
        # 从最新消息开始往前倒推，找到保留的消息
        kept_messages = []
        kept_tokens = system_tokens
        
        # 从后往前遍历
        for i in range(len(other_messages) - 1, -1, -1):
            msg = other_messages[i]
            msg_tokens = self._estimate_tokens(msg.content or "")
            
            # 检查加上这条消息是否超过限制
            if kept_tokens + msg_tokens > fc_max_tokens:
                break
                
            kept_messages.insert(0, msg)
            kept_tokens += msg_tokens
        
        # 确保保持用户-助手对话的完整性
        # 如果保留的消息以assistant开头，需要找到对应的user消息
        if kept_messages and kept_messages[0].role == "assistant":
            # 找到这个assistant消息对应的user消息
            first_kept_index = other_messages.index(kept_messages[0])
            
            # 往前找user消息，确保保持对话完整性
            for j in range(first_kept_index - 1, -1, -1):
                if other_messages[j].role == "user":
                    # 检查加上这个user消息及其之前的assistant消息是否过多
                    # 为了保持完整性，我们移除第一个assistant消息
                    if len(kept_messages) > 0:
                        kept_messages.pop(0)
                    break
        
        # 计算需要保存到memory的消息
        if kept_messages:
            first_kept_index = other_messages.index(kept_messages[0])
            messages_to_save = other_messages[:first_kept_index]
        else:
            # 如果没有保留的消息，保存所有消息（除了最后一对）
            if len(other_messages) >= 2:
                messages_to_save = other_messages[:-2]
                kept_messages = other_messages[-2:]
            else:
                messages_to_save = []
                kept_messages = other_messages
        
        # 如果有消息需要保存到memory
        memory_messages = []
        memory_summary = ""
        if messages_to_save:
            try:
                # 调用memory的save_memory保存超出的消息
                await memory.save_memory(messages_to_save)

                # 从memory获取历史记录摘要（已在源头进行过policy check）
                memory_summary = await memory.query_memory("")
                
                # 重构后只返回摘要字符串，不再返回原始对话
                memory_messages = []  # 不再使用历史消息列表

                # 从对话中移除已保存的消息
                for msg in messages_to_save:
                    if msg in self.dialogue:
                        self.dialogue.remove(msg)

            except Exception as e:
                # 如果保存失败，记录错误但继续处理
                print(f"Memory save/query failed: {e}")

        # 构建最终的对话列表
        final_dialogue = []

        # 添加system消息，并将历史总结合并到system prompt中
        for msg in system_messages:
            system_content = msg.content or ""

            # 如果有历史总结，将其合并到system prompt中
            if memory_summary:
                system_content += f"历史对话总结：{memory_summary}"

            final_dialogue.append({"role": "system", "content": system_content})

        # 重构后不再添加历史消息，只使用摘要
        
        # 添加保留的消息
        for msg in kept_messages:
            if msg.tool_calls is not None:
                final_dialogue.append({"role": msg.role, "tool_calls": msg.tool_calls})
            elif msg.role == "tool":
                final_dialogue.append({
                    "role": msg.role,
                    "tool_call_id": msg.tool_call_id or str(uuid.uuid4()),
                    "content": msg.content,
                })
            else:
                final_dialogue.append({"role": msg.role, "content": msg.content})
        
        return final_dialogue


def filter_sensitive_info(config: dict) -> dict:
    """
    过滤配置中的敏感信息
    Args:
        config: 原始配置字典
    Returns:
        过滤后的配置字典
    """
    sensitive_keys = [
        "api_key",
        "personal_access_token",
        "access_token",
        "token",
        "secret",
        "access_key_secret",
        "secret_key",
    ]

    def _filter_dict(d: dict) -> dict:
        filtered = {}
        for k, v in d.items():
            if any(sensitive in k.lower() for sensitive in sensitive_keys):
                filtered[k] = "***"
            elif isinstance(v, dict):
                filtered[k] = _filter_dict(v)
            elif isinstance(v, list):
                filtered[k] = [_filter_dict(i) if isinstance(i, dict) else i for i in v]
            else:
                filtered[k] = v
        return filtered

    return _filter_dict(copy.deepcopy(config))


def find_text_split_point(text, min_segment_length=4, logger=None):
    """
    查找文本的合适分割点，用于流式TTS处理
    支持最小分段长度限制，避免过短片段
    
    Args:
        text: 待分割的文本
        min_segment_length: 最小分段长度，默认4个字符
        logger: 日志记录器（可选）
        
    Returns:
        int: 分割点位置，-1表示未找到合适分割点
    """
    if not text:
        return -1
    
    # 所有标点符号都可以分段，按优先级排序
    all_punctuations = ["。", "？", "?", "！", "!", "，", ",", "；", ";", "：", "~", "～"]
    
    # 收集所有标点符号位置
    punct_positions = []
    for punct in all_punctuations:
        pos = 0
        while True:
            pos = text.find(punct, pos)
            if pos == -1:
                break
            if pos > 0:  # 不在开头
                punct_positions.append(pos)
            pos += 1
    
    # 按位置排序
    punct_positions.sort()
    
    if logger:
        logger.debug(f"🔍 找到标点位置: {punct_positions}")
    
    # 依次检查每个标点位置
    for punct_pos in punct_positions:
        potential_segment = text[:punct_pos + 1]
        cleaned_segment = get_string_no_punctuation_or_emoji(potential_segment)
        
        if logger:
            logger.debug(f"🔍 检查位置 {punct_pos}:")
            logger.debug(f"  potential_segment: '{potential_segment}'")
            logger.debug(f"  cleaned_segment: '{cleaned_segment}'")
            logger.debug(f"  cleaned_length: {len(cleaned_segment)}")
        
        if len(cleaned_segment) >= min_segment_length:
            if logger:
                logger.debug(f"  ✅ 长度满足要求，选择此分割点")
            return punct_pos
        else:
            if logger:
                logger.debug(f"  ❌ 长度不足，继续寻找")
    
    if logger:
        logger.debug(f"🔍 未找到满足长度要求的分割点")
    return -1


def segment_text_with_merge(text, min_segment_length=4, logger=None):
    """
    对文本进行分段，并合并过短的片段
    
    Args:
        text: 待分段的文本
        min_segment_length: 最小分段长度，默认4个字符
        logger: 日志记录器（可选）
        
    Returns:
        list: 分段后的文本列表，短句已合并
    """
    if not text:
        return []
    
    segments = []
    processed = 0
    
    while processed < len(text):
        current_text = text[processed:]
        split_pos = find_text_split_point(current_text, min_segment_length, logger)
        
        if split_pos != -1:
            segment = current_text[:split_pos + 1].strip()
            if segment:
                segments.append(segment)
                processed += len(current_text[:split_pos + 1])
        else:
            # 没找到分割点，处理剩余文本
            if current_text.strip():
                segments.append(current_text.strip())
            break
    
    # 合并短句（< 4个字符的有效内容）
    merged_segments = []
    i = 0
    
    while i < len(segments):
        current_segment = segments[i]
        
        # 检查当前段是否太短（去掉标点后少于4个字符）
        clean_current = current_segment.rstrip("。？?！!；;：，,~～")
        
        if len(clean_current) < 4 and i + 1 < len(segments):
            # 当前段太短，与下一段合并
            next_segment = segments[i + 1]
            merged_segment = current_segment + next_segment
            merged_segments.append(merged_segment)
            if logger:
                logger.debug(f"合并短句: '{current_segment}' + '{next_segment}' -> '{merged_segment}'")
            i += 2  # 跳过下一段，因为已经合并了
        else:
            # 当前段长度合适，直接添加
            merged_segments.append(current_segment)
            i += 1
    
    return merged_segments
