import json
import time
from typing import Dict, Any, Optional, Tuple

import redis
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class RedisClient:
    """Redis 客户端，用于管理用户配置信息和聊天记录"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化 Redis 客户端
        
        Args:
            config: 包含 Redis 配置的字典
        """
        # 尝试从不同位置获取 Redis 配置
        if "server" in config and "redis" in config["server"]:
            self.redis_config = config["server"]["redis"]
        elif "redis" in config:
            self.redis_config = config["redis"]
        else:
            self.redis_config = {}
        self.enabled = self.redis_config.get("enabled", False)
        self.client = None
        self.key_prefix = self.redis_config.get("key_prefix", "yuyan:v1:")
        self.expiration = self.redis_config.get("expiration", 2592000)  # 默认30天
        
        if self.enabled:
            try:
                host = self.redis_config.get("host", "127.0.0.1")
                port = self.redis_config.get("port", 6379)
                self.client = redis.Redis(
                    host=host,
                    port=port,
                    db=self.redis_config.get("db", 0),
                    password=self.redis_config.get("password", ""),
                    decode_responses=True,  # 自动将响应解码为字符串
                    socket_connect_timeout=5,  # 连接超时时间
                    socket_timeout=5  # 操作超时时间
                )
                # 测试连接
                self.client.ping()
                logger.bind(tag=TAG).info(f"已连接 redis {host}:{port}")
            except Exception as e:
                error_msg = f"redis 服务未启动: {str(e)}\n请检查 .config.yaml 中配置的 redis server"
                logger.bind(tag=TAG).error(error_msg)
                self.client = None
                self.enabled = False
                # 如果 Redis 启用但连接失败，抛出异常使应用程序退出
                raise ConnectionError(error_msg)
    
    def get_user_profile_key(self, client_id: str) -> str:
        """获取用户配置在 Redis 中的键名

        Args:
            client_id: 客户端 ID

        Returns:
            Redis 键名
        """
        return f"{self.key_prefix}profile:{client_id}"

    def get_user_memory_key(self, client_id: str) -> str:
        """获取用户聊天记录在 Redis 中的键名

        Args:
            client_id: 客户端 ID

        Returns:
            Redis 键名
        """
        return f"{self.key_prefix}memory:{client_id}"

    def check_redis_sever(self):
        """检查Redis服务器是否连接成功"""
        if not self.enabled or not self.client:
            return False
        try:
            self.client.ping()
            return True
        except Exception as e:
            error_msg = f"redis 服务未启动: {str(e)}\n请检查 .config.yaml 中配置的 redis server"
            raise ConnectionError(error_msg)
    
    def get_user_data(self, client_id: str) -> Optional[Dict[str, Any]]:
        """获取用户配置数据

        Args:
            client_id: 客户端 ID

        Returns:
            用户配置数据字典，如果不存在则返回 None
        """
        if not self.enabled or not self.client:
            logger.bind(tag=TAG).debug(f"Redis未启用或客户端不可用，无法获取用户配置: client_id={client_id}")
            return None

        try:
            profile_key = self.get_user_profile_key(client_id)
            profile_data_str = self.client.get(profile_key)

            if not profile_data_str:
                logger.bind(tag=TAG).debug(f"Redis中未找到用户配置数据: client_id={client_id}")
                return None

            # 更新访问时间（刷新过期时间）
            self.client.expire(profile_key, self.expiration)

            profile_data = json.loads(profile_data_str)
            logger.bind(tag=TAG).debug(f"成功从Redis获取用户配置数据: client_id={client_id}, 完整数据={json.dumps(profile_data, ensure_ascii=False, indent=2)}")
            return profile_data
        except Exception as e:
            logger.bind(tag=TAG).error(f"从Redis获取用户配置数据失败: client_id={client_id}, 错误={str(e)}")
            return None
    
    def save_user_data(self, client_id: str, user_data: Dict[str, Any]) -> bool:
        """保存用户配置数据

        Args:
            client_id: 客户端 ID
            user_data: 用户配置数据字典

        Returns:
            是否保存成功
        """
        if not self.enabled or not self.client:
            logger.bind(tag=TAG).debug(f"Redis未启用或客户端不可用，无法保存用户配置: client_id={client_id}")
            return False

        try:
            profile_key = self.get_user_profile_key(client_id)

            # 更新访问时间
            user_data["last_access_time"] = int(time.time())

            # 保存配置数据
            self.client.set(profile_key, json.dumps(user_data), ex=self.expiration)
            logger.bind(tag=TAG).debug(f"成功保存用户配置数据到Redis: client_id={client_id}, 完整数据={json.dumps(user_data, ensure_ascii=False, indent=2)}")
            return True
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存用户配置数据到Redis失败: client_id={client_id}, 错误={str(e)}")
            return False
    
    def get_memory(self, client_id: str) -> Optional[str]:
        """获取用户聊天记录数据

        Args:
            client_id: 客户端 ID

        Returns:
            用户聊天记录数据，如果不存在则返回 None
        """
        if not self.enabled or not self.client:
            logger.bind(tag=TAG).debug(f"Redis未启用或客户端不可用，无法获取聊天记录: client_id={client_id}")
            return None

        try:
            memory_key = self.get_user_memory_key(client_id)
            memory_data = self.client.get(memory_key)

            if not memory_data:
                logger.bind(tag=TAG).debug(f"Redis中未找到聊天记录数据: client_id={client_id}")
                return None

            # 更新访问时间（刷新过期时间）
            self.client.expire(memory_key, self.expiration)

            logger.bind(tag=TAG).info(f"成功从Redis获取聊天记录数据: client_id={client_id}, 数据长度={len(memory_data)}字符, 原始数据={memory_data}")
            return memory_data
        except Exception as e:
            logger.bind(tag=TAG).error(f"从Redis获取聊天记录数据失败: client_id={client_id}, 错误={str(e)}")
            return None
    
    def save_memory(self, client_id: str, memory_data: str) -> bool:
        """保存用户聊天记录数据

        Args:
            client_id: 客户端 ID
            memory_data: 聊天记录数据

        Returns:
            是否保存成功
        """
        if not self.enabled or not self.client:
            logger.bind(tag=TAG).debug(f"Redis未启用或客户端不可用，无法保存聊天记录: client_id={client_id}")
            return False

        try:
            memory_key = self.get_user_memory_key(client_id)

            # 直接保存聊天记录数据到独立的键
            self.client.set(memory_key, memory_data, ex=self.expiration)
            
            # 尝试解析JSON以获取更详细的信息用于日志
            try:
                parsed_data = json.loads(memory_data)
                if isinstance(parsed_data, dict):
                    chat_history = parsed_data.get("chat_history", [])
                    last_update = parsed_data.get("last_update_time", "未知")
                    logger.bind(tag=TAG).info(f"成功保存聊天记录数据到Redis: client_id={client_id}, 数据长度={len(memory_data)}字符, 聊天条数={len(chat_history)}, 最后更新={last_update}, 完整数据={json.dumps(parsed_data, ensure_ascii=False, indent=2)}")
                else:
                    logger.bind(tag=TAG).info(f"成功保存聊天记录数据到Redis: client_id={client_id}, 数据长度={len(memory_data)}字符, 原始数据={memory_data}")
            except (json.JSONDecodeError, Exception):
                logger.bind(tag=TAG).info(f"成功保存聊天记录数据到Redis: client_id={client_id}, 数据长度={len(memory_data)}字符, 原始数据={memory_data}")
            
            return True
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存聊天记录数据到Redis失败: client_id={client_id}, 错误={str(e)}")
            return False

    def delete_user_profile(self, client_id: str) -> bool:
        """删除用户配置数据

        Args:
            client_id: 客户端 ID

        Returns:
            是否删除成功
        """
        if not self.enabled or not self.client:
            return False

        try:
            profile_key = self.get_user_profile_key(client_id)
            result = self.client.delete(profile_key)
            logger.bind(tag=TAG).info(f"删除用户配置数据: client_id={client_id}, 结果={'成功' if result else '未找到'}")
            return bool(result)
        except Exception as e:
            logger.bind(tag=TAG).error(f"删除用户配置数据失败: client_id={client_id}, 错误={str(e)}")
            return False

    def delete_user_memory(self, client_id: str) -> bool:
        """删除用户聊天记录数据

        Args:
            client_id: 客户端 ID

        Returns:
            是否删除成功
        """
        if not self.enabled or not self.client:
            return False

        try:
            memory_key = self.get_user_memory_key(client_id)
            result = self.client.delete(memory_key)
            logger.bind(tag=TAG).info(f"删除聊天记录数据: client_id={client_id}, 结果={'成功' if result else '未找到'}")
            return bool(result)
        except Exception as e:
            logger.bind(tag=TAG).error(f"删除聊天记录数据失败: client_id={client_id}, 错误={str(e)}")
            return False

    def delete_all_user_data(self, client_id: str) -> Tuple[bool, bool]:
        """删除用户的所有数据（配置和聊天记录）

        Args:
            client_id: 客户端 ID

        Returns:
            (配置删除结果, 聊天记录删除结果)
        """
        profile_deleted = self.delete_user_profile(client_id)
        memory_deleted = self.delete_user_memory(client_id)
        return profile_deleted, memory_deleted
