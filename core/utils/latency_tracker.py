import time
from typing import Dict, Optional
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class LatencyTracker:
    """
    用于跟踪和记录各组件延迟的工具类
    """
    def __init__(self):
        self.start_times: Dict[str, float] = {}
        self.end_times: Dict[str, float] = {}
        self.durations: Dict[str, float] = {}
    
    def start(self, component: str) -> None:
        """
        开始计时特定组件
        """
        self.start_times[component] = time.time() * 1000  # 转换为毫秒
    
    def end(self, component: str) -> float:
        """
        结束计时特定组件并返回延迟时间（毫秒）
        """
        if component not in self.start_times:
            logger.bind(tag=TAG).warning(f"尝试结束未开始计时的组件: {component}")
            return 0
        
        self.end_times[component] = time.time() * 1000  # 转换为毫秒
        duration = self.end_times[component] - self.start_times[component]
        self.durations[component] = duration
        return duration
    
    def get_duration(self, component: str) -> Optional[float]:
        """
        获取特定组件的延迟时间（毫秒）
        """
        return self.durations.get(component)
    
    def log_latency(self, component: str) -> None:
        """
        记录特定组件的延迟到日志
        """
        duration = self.get_duration(component)
        if duration is not None:
            logger.bind(tag=TAG).info(f"{component}延迟: {duration:.2f}毫秒")
    
    def log_all_latencies(self) -> None:
        """
        记录所有组件的延迟到日志
        """
        for component, duration in self.durations.items():
            logger.bind(tag=TAG).info(f"{component}延迟: {duration:.2f}毫秒")
