"""
线程池工具模块
提供带有daemon线程支持的ThreadPoolExecutor实现
"""

import threading
import weakref
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures.thread import _worker, _threads_queues


class DaemonThreadPoolExecutor(ThreadPoolExecutor):
    """
    ThreadPoolExecutor that creates daemon threads to prevent CPU hang issues
    
    这个自定义的ThreadPoolExecutor强制所有工作线程都是daemon线程，
    这样可以防止在客户端断开连接后出现CPU 100%占用的问题。
    
    daemon线程的特点：
    - 主进程退出时会自动终止
    - 不会阻止进程正常退出
    - 防止僵尸线程无限占用CPU资源
    """
    
    def _adjust_thread_count(self):
        """
        重写父类方法，强制创建daemon线程
        
        这个方法会在需要新线程时被调用，我们在这里强制设置
        daemon=True 来确保所有工作线程都是daemon线程。
        """
        # if idle threads are available, don't spin new threads
        if self._idle_semaphore.acquire(timeout=0):
            return

        # When the executor gets lost, the weakref callback will wake up
        # the worker threads.
        def weakref_cb(_, q=self._work_queue):
            q.put(None)

        num_threads = len(self._threads)
        if num_threads < self._max_workers:
            thread_name = '%s_%d' % (self._thread_name_prefix or self, num_threads)
            # 关键修复：强制设置daemon=True
            t = threading.Thread(name=thread_name, target=_worker,
                                args=(weakref.ref(self, weakref_cb),
                                      self._work_queue,
                                      self._initializer,
                                      self._initargs),
                                daemon=True)  # 强制设置为daemon线程
            t.start()
            self._threads.add(t)
            _threads_queues[t] = self._work_queue