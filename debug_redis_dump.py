#!/usr/bin/env python3
"""
Redis数据导出脚本 - 用于排查用户profile和聊天记录混乱问题

功能：
1. 导出所有用户profile相关的key-value
2. 导出所有聊天记录相关的key-value  
3. 分析数据结构和潜在问题
4. 格式化输出便于排查
"""

import json
import time
import redis
from datetime import datetime
from typing import Dict, Any, List, Tuple
import argparse
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config_loader import load_config


def get_redis_client():
    """获取Redis客户端连接"""
    try:
        config = load_config()
        
        # 尝试从不同位置获取 Redis 配置
        if "server" in config and "redis" in config["server"]:
            redis_config = config["server"]["redis"]
        elif "redis" in config:
            redis_config = config["redis"]
        else:
            print("❌ 未找到Redis配置")
            return None
            
        if not redis_config.get("enabled", False):
            print("❌ Redis未启用")
            return None
            
        host = redis_config.get("host", "127.0.0.1")
        port = redis_config.get("port", 6379)
        db = redis_config.get("db", 0)
        password = redis_config.get("password", "")
        key_prefix = redis_config.get("key_prefix", "yuyan:v1:")
        
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        
        # 测试连接
        client.ping()
        print(f"✅ 成功连接到Redis: {host}:{port} (db={db})")
        
        return client, key_prefix
        
    except Exception as e:
        print(f"❌ 连接Redis失败: {e}")
        return None, None


def format_timestamp(timestamp):
    """格式化时间戳"""
    if timestamp is None:
        return "未知"
    try:
        if isinstance(timestamp, str):
            timestamp = float(timestamp)
        return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    except:
        return str(timestamp)


def analyze_profile_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """分析用户profile数据"""
    analysis = {
        "fields": list(data.keys()),
        "has_role": "role" in data,
        "has_user_name": "user_name" in data,
        "has_assistant_name": "assistant_name" in data,
        "has_profile": "profile" in data,
        "has_preferences": "preferences" in data,
        "last_access": format_timestamp(data.get("last_access_time")),
        "profile_update": format_timestamp(data.get("profile_last_update"))
    }
    
    # 分析profile内容
    if "profile" in data:
        profile = data["profile"]
        if isinstance(profile, dict):
            analysis["profile_fields"] = list(profile.keys())
            analysis["profile_name"] = profile.get("name", "未设置")
        else:
            analysis["profile_error"] = f"profile不是字典类型: {type(profile)}"
    
    # 分析preferences内容
    if "preferences" in data:
        preferences = data["preferences"]
        if isinstance(preferences, dict):
            analysis["preferences_fields"] = list(preferences.keys())
        else:
            analysis["preferences_error"] = f"preferences不是字典类型: {type(preferences)}"
    
    return analysis


def analyze_memory_data(data: str) -> Dict[str, Any]:
    """分析聊天记录数据"""
    analysis = {
        "data_length": len(data),
        "is_json": False,
        "content_type": "raw_string"
    }
    
    try:
        parsed = json.loads(data)
        analysis["is_json"] = True
        analysis["content_type"] = "json"
        
        if isinstance(parsed, dict):
            analysis["json_fields"] = list(parsed.keys())
            
            # 分析summary
            if "summary" in parsed:
                summary = parsed["summary"]
                analysis["has_summary"] = True
                analysis["summary_length"] = len(summary) if summary else 0
                analysis["summary_preview"] = summary[:100] + "..." if len(summary) > 100 else summary
            
            # 分析chat_history
            if "chat_history" in parsed:
                chat_history = parsed["chat_history"]
                analysis["has_chat_history"] = True
                if isinstance(chat_history, list):
                    analysis["chat_count"] = len(chat_history)
                    if chat_history:
                        analysis["first_message_role"] = chat_history[0].get("role", "unknown")
                        analysis["last_message_role"] = chat_history[-1].get("role", "unknown")
                else:
                    analysis["chat_history_error"] = f"chat_history不是列表类型: {type(chat_history)}"
            
            # 分析时间戳
            if "last_update_time" in parsed:
                analysis["last_update"] = format_timestamp(parsed["last_update_time"])
                
        else:
            analysis["json_error"] = f"JSON解析结果不是字典类型: {type(parsed)}"
            
    except json.JSONDecodeError as e:
        analysis["json_error"] = f"JSON解析失败: {e}"
        analysis["raw_preview"] = data[:200] + "..." if len(data) > 200 else data
    
    return analysis


def dump_redis_data(output_file: str = None, verbose: bool = False):
    """导出Redis中的用户数据"""
    
    client, key_prefix = get_redis_client()
    if not client:
        return
    
    print(f"🔍 开始扫描Redis数据，key前缀: {key_prefix}")
    print("=" * 80)
    
    # 获取所有相关的key
    profile_pattern = f"{key_prefix}profile:*"
    memory_pattern = f"{key_prefix}memory:*"
    
    profile_keys = client.keys(profile_pattern)
    memory_keys = client.keys(memory_pattern)
    
    print(f"📊 找到 {len(profile_keys)} 个用户profile keys")
    print(f"📊 找到 {len(memory_keys)} 个聊天记录 keys")
    print("=" * 80)
    
    # 收集所有数据
    all_data = {
        "scan_time": datetime.now().isoformat(),
        "key_prefix": key_prefix,
        "profiles": {},
        "memories": {},
        "analysis": {
            "profile_count": len(profile_keys),
            "memory_count": len(memory_keys),
            "client_ids": set()
        }
    }
    
    # 导出profile数据
    print("🔍 分析用户Profile数据:")
    print("-" * 40)
    
    for key in profile_keys:
        try:
            # 提取client_id
            client_id = key.replace(f"{key_prefix}profile:", "")
            all_data["analysis"]["client_ids"].add(client_id)
            
            # 获取数据
            raw_data = client.get(key)
            if raw_data:
                try:
                    profile_data = json.loads(raw_data)
                    analysis = analyze_profile_data(profile_data)
                    
                    all_data["profiles"][client_id] = {
                        "key": key,
                        "raw_data": profile_data,
                        "analysis": analysis,
                        "ttl": client.ttl(key)
                    }
                    
                    print(f"📋 Client ID: {client_id}")
                    print(f"   Key: {key}")
                    print(f"   Fields: {analysis['fields']}")
                    print(f"   User Name: {profile_data.get('user_name', 'N/A')}")
                    print(f"   Role: {profile_data.get('role', 'N/A')}")
                    print(f"   Assistant Name: {profile_data.get('assistant_name', 'N/A')}")
                    print(f"   Last Access: {analysis['last_access']}")
                    print(f"   TTL: {client.ttl(key)}s")
                    
                    if verbose and "profile" in profile_data:
                        print(f"   Profile: {json.dumps(profile_data['profile'], ensure_ascii=False, indent=6)}")
                    
                    print()
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Profile JSON解析失败 {key}: {e}")
                    all_data["profiles"][client_id] = {
                        "key": key,
                        "raw_data": raw_data,
                        "error": f"JSON解析失败: {e}",
                        "ttl": client.ttl(key)
                    }
            else:
                print(f"⚠️  Profile key存在但无数据: {key}")
                
        except Exception as e:
            print(f"❌ 处理profile key失败 {key}: {e}")
    
    # 导出memory数据
    print("🔍 分析聊天记录数据:")
    print("-" * 40)
    
    for key in memory_keys:
        try:
            # 提取client_id
            client_id = key.replace(f"{key_prefix}memory:", "")
            all_data["analysis"]["client_ids"].add(client_id)
            
            # 获取数据
            raw_data = client.get(key)
            if raw_data:
                analysis = analyze_memory_data(raw_data)
                
                all_data["memories"][client_id] = {
                    "key": key,
                    "raw_data": raw_data if len(raw_data) < 5000 else raw_data[:5000] + "...[truncated]",
                    "analysis": analysis,
                    "ttl": client.ttl(key)
                }
                
                print(f"💬 Client ID: {client_id}")
                print(f"   Key: {key}")
                print(f"   Data Length: {analysis['data_length']} 字符")
                print(f"   Content Type: {analysis['content_type']}")
                print(f"   TTL: {client.ttl(key)}s")
                
                if analysis["is_json"]:
                    print(f"   JSON Fields: {analysis.get('json_fields', [])}")
                    if "has_summary" in analysis:
                        print(f"   Has Summary: {analysis['has_summary']} (length: {analysis.get('summary_length', 0)})")
                    if "has_chat_history" in analysis:
                        print(f"   Has Chat History: {analysis['has_chat_history']} (count: {analysis.get('chat_count', 0)})")
                    if "last_update" in analysis:
                        print(f"   Last Update: {analysis['last_update']}")
                
                if verbose and analysis["is_json"]:
                    try:
                        parsed = json.loads(raw_data)
                        print(f"   Content Preview: {json.dumps(parsed, ensure_ascii=False, indent=6)[:500]}...")
                    except:
                        pass
                
                print()
                
            else:
                print(f"⚠️  Memory key存在但无数据: {key}")
                
        except Exception as e:
            print(f"❌ 处理memory key失败 {key}: {e}")
    
    # 转换set为list以便JSON序列化
    all_data["analysis"]["client_ids"] = list(all_data["analysis"]["client_ids"])
    
    # 数据交叉分析
    print("🔍 数据交叉分析:")
    print("-" * 40)
    
    profile_clients = set(all_data["profiles"].keys())
    memory_clients = set(all_data["memories"].keys())
    all_clients = profile_clients.union(memory_clients)
    
    print(f"👥 总共发现 {len(all_clients)} 个不同的客户端ID")
    print(f"📋 有Profile的客户端: {len(profile_clients)} 个")
    print(f"💬 有Memory的客户端: {len(memory_clients)} 个")
    
    # 只有profile没有memory的客户端
    profile_only = profile_clients - memory_clients
    if profile_only:
        print(f"⚠️  只有Profile没有Memory的客户端 ({len(profile_only)} 个): {list(profile_only)}")
    
    # 只有memory没有profile的客户端
    memory_only = memory_clients - profile_clients
    if memory_only:
        print(f"⚠️  只有Memory没有Profile的客户端 ({len(memory_only)} 个): {list(memory_only)}")
    
    # 完整数据的客户端
    complete_clients = profile_clients.intersection(memory_clients)
    if complete_clients:
        print(f"✅ 同时有Profile和Memory的客户端 ({len(complete_clients)} 个): {list(complete_clients)}")
    
    all_data["analysis"]["profile_only_clients"] = list(profile_only)
    all_data["analysis"]["memory_only_clients"] = list(memory_only)
    all_data["analysis"]["complete_clients"] = list(complete_clients)
    
    # 输出到文件
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            print(f"📄 数据已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    print("=" * 80)
    print("✅ Redis数据导出完成")
    
    return all_data


def main():
    parser = argparse.ArgumentParser(description="Redis用户数据导出工具")
    parser.add_argument("-o", "--output", help="输出文件路径 (默认不保存文件)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出模式")
    
    args = parser.parse_args()
    
    # 默认输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"redis_dump_{timestamp}.json"
    
    dump_redis_data(args.output, args.verbose)


if __name__ == "__main__":
    main()