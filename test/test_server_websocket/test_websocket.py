#!/usr/bin/env python3
"""
WebSocket 服务健康检查脚本

该脚本用于测试 yuyan-server 的 WebSocket 服务是否正常运行。
它会：
1. 连接到指定的 WebSocket 地址
2. 发送 hello 握手消息
3. 发送一条测试文本消息
4. 验证服务端返回的 hello 响应
5. 验证服务端返回的文本和语音响应
6. 验证语音文件可以正常播放
"""

import asyncio
import json
import os
import socket
import sys
import tempfile
import time
import uuid
import websockets
from pathlib import Path
from typing import Dict, Any, Optional
import subprocess
import platform
import argparse
import logging
from urllib.parse import urlparse, urlunparse, parse_qs, urlencode
import base64

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 默认配置
DEFAULT_WS_URL = "wss://chat-api-yuyan.zzfx.net.cn:443/xiaozhi/v1"  # WebSocket 端点
TEST_MESSAGE = "你好，这是一条测试消息"

# 平台特定的播放命令
PLAY_CMDS = {
    'darwin': 'afplay',  # macOS
    'linux': 'aplay',    # Linux (requires alsa-utils)
    'win32': 'start',    # Windows
}

class WebSocketTester:
    def __init__(self, ws_url: str, test_message: str, timeout: int = 10):
        """
        初始化 WebSocket 测试器
        
        Args:
            ws_url: WebSocket 服务器地址
            test_message: 要发送的测试消息
            timeout: 超时时间（秒）
        """
        # 生成随机的 client_id 和 device_id 作为实例属性
        self.client_id = f"test_client_{uuid.uuid4().hex[:8]}"
        self.device_id = f"test_device_{uuid.uuid4().hex[:8]}"
        
        # 初始化日志记录器 (必须在 client_id 之后)
        self.logger = logging.getLogger(f"WebSocketTester-{self.client_id[-8:]}")
        self.logger.setLevel(logging.INFO)
        
        self.ws_url = ws_url
        self.test_message = test_message
        self.timeout = timeout
        self.received_messages = []
        self.audio_data = b''
        self.audio_file = None
        self.hello_received = False
        self.text_response_received = False
        self.audio_response_received = False
        self.play_cmd = None  # Remove audio playback capability
        
        # 认证配置
        self.token = os.getenv('AUTH_TOKEN', 'test_token')  # 从环境变量获取token，默认为'test_token'
        
        # 默认配置
        self.config = {
            "auth": {
                "enabled": True,
                "auth_type": "static",
                "tokens": [
                    {
                        "token": self.token,
                        "name": "test_client"
                    }
                ]
            }
        }
        
        self.audio_params = None  # Add this to store server's audio params
        self.tts_stop_received = False  # Add flag for TTS stop message
    
    async def connect(self) -> bool:
        """连接到 WebSocket 服务器"""
        try:
            print(f"正在连接到 {self.ws_url}...")
            
            # 准备请求头
            headers = [
                ('device-id', self.device_id),
                ('client-id', self.client_id),
                ('Authorization', f'Bearer {self.token}')
            ]
            
            print(f"使用认证头: {dict(headers)}")
            
            # 解析URL并添加查询参数
            parsed_url = urlparse(self.ws_url)
            query_params = parse_qs(parsed_url.query)
            query_params['device-id'] = [self.device_id]
            query_params['client-id'] = [self.client_id]
            
            # 重新构建URL
            new_query = urlencode(query_params, doseq=True)
            ws_url = urlunparse(parsed_url._replace(query=new_query))
            
            # 创建连接选项
            options = {
                'ping_interval': None,
                'close_timeout': 1,
                'max_size': 2**25,  # 32MB
                'max_queue': 2**8,   # 256
            }
            
            # 创建连接（只连接一次）
            self.websocket = await websockets.connect(
                ws_url,
                additional_headers=dict(headers),
                **options
            )
            
            print("连接成功")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            if hasattr(self, 'websocket') and hasattr(self.websocket, 'close'):
                try:
                    if not self.websocket.closed:
                        await self.websocket.close()
                except:
                    pass
            return False
    
    async def send_hello(self) -> bool:
        """发送 hello 握手消息"""
        try:
            # 使用连接时生成的 client_id 和 device_id
            session_id = str(uuid.uuid4())
            
            # 获取本机IP
            try:
                # 创建一个UDP套接字（不建立实际连接）
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                # 连接到公共DNS服务器（这里使用Google的*******）
                s.connect(('*******', 80))
                local_ip = s.getsockname()[0]
                s.close()
            except Exception:
                # 如果获取失败，使用回环地址
                local_ip = '127.0.0.1'
            
            hello_msg = {
                "type": "hello",
                "client": "health_check",
                "version": "1.0.0",
                "client_id": self.client_id,
                "device_id": self.device_id,
                "session_id": session_id,
                "client_ip": local_ip,
                "platform": "test",
                "model": "test_script",
                "audio_params": {
                    "format": "pcm",
                    "sample_rate": 16000,
                    "channels": 1,
                    "sample_width": 2
                },
                "timestamp": int(time.time() * 1000),
                "sdk_version": "1.0.0",
                "capabilities": ["text", "audio"],
                "supported_audio_formats": ["pcm", "wav"]
            }
            
            # 添加认证信息（如果需要）
            auth_config = self.config.get("auth", {})
            if auth_config.get("enabled", False):
                hello_msg["auth"] = {
                    "token": "test_token",
                    "expires_in": 3600
                }
            
            print(f"发送 hello 消息: {json.dumps(hello_msg, ensure_ascii=False, indent=2)}")
            
            # 发送 hello 消息
            await self.websocket.send(json.dumps(hello_msg))
            
            # 等待服务器响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=self.timeout)
            print(f"收到原始响应: {response}")
            
            try:
                response_data = json.loads(response)
                print(f"解析响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                self.hello_received = True  # 设置握手完成标志
                self.session_id = response_data.get('session_id')  # 保存会话ID
                
                # Store server's audio parameters
                self.audio_params = response_data.get('audio_params', {})
                print(f"Server audio format: {self.audio_params.get('format', 'unknown')}")
                
                return True
            except json.JSONDecodeError as e:
                print(f"解析响应失败: {e}, 原始响应: {response}")
                return False
                
        except asyncio.TimeoutError:
            print("等待 hello 响应超时")
            return False
        except websockets.exceptions.ConnectionClosed as e:
            print(f"WebSocket 连接已关闭: {e}")
            return False
        except Exception as e:
            print(f"发送/接收 hello 消息时出错: {e}")
            return False
    
    async def send_text_message(self) -> bool:
        """发送文本消息并处理响应"""
        if not self.hello_received:
            print("错误: 请先完成 hello 握手")
            return False
            
        # 使用与test_page.html相同的消息格式
        text_msg = {
            "type": "listen",
            "mode": "manual",
            "state": "detect",
            "text": self.test_message
        }
        
        try:
            print(f"发送文本消息: {self.test_message}")
            await self.websocket.send(json.dumps(text_msg, ensure_ascii=False))
            
            # 创建临时目录保存音频文件
            os.makedirs('temp_audio', exist_ok=True)
            audio_chunks = []
            last_activity = time.time()
            
            # 等待服务器响应
            while time.time() - last_activity < self.timeout:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1)
                    last_activity = time.time()
                    
                    # 处理二进制音频数据
                    if isinstance(message, bytes):
                        print(f"收到音频数据: {len(message)} 字节")
                        audio_chunks.append(message)
                        self.audio_response_received = True
                        continue
                        
                    # 处理文本消息
                    try:
                        response_data = json.loads(message)
                        print(f"收到JSON响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                        
                        # 处理不同类型的消息
                        msg_type = response_data.get('type')
                        if msg_type == 'tts':
                            state = response_data.get('state')
                            if state == 'sentence_start':
                                print(f"TTS开始: {response_data.get('text', '')}")
                            elif state == 'sentence_end':
                                print(f"TTS结束: {response_data.get('text', '')}")
                        
                        self.received_messages.append(('response', response_data))
                        
                    except json.JSONDecodeError:
                        print(f"收到非JSON文本消息: {message}")
                    
                except asyncio.TimeoutError:
                    # 如果没有收到数据，检查是否应该继续等待
                    if audio_chunks and time.time() - last_activity > 2.0:
                        # 如果已经有音频数据且2秒没有新数据，认为接收完成
                        break
                    continue
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    continue
            
            # 保存音频文件
            if audio_chunks:
                audio_data = b''.join(audio_chunks)
                audio_path = f"temp_audio/response_{int(time.time())}.wav"
                
                # 添加WAV文件头（假设是16kHz, 16位, 单声道PCM）
                import struct
                sample_rate = 16000
                channels = 1
                bits_per_sample = 16
                
                # 创建WAV文件头
                wav_header = struct.pack(
                    '<4sI4s4sIHHIIHH4sI',
                    b'RIFF',
                    36 + len(audio_data),  # 文件总大小 - 8
                    b'WAVE',
                    b'fmt ', 16,  # fmt块大小
                    1,  # PCM格式
                    channels,
                    sample_rate,
                    sample_rate * channels * bits_per_sample // 8,  # 字节率
                    channels * bits_per_sample // 8,  # 块对齐
                    bits_per_sample,
                    b'data',
                    len(audio_data)  # 数据大小
                )
                
                with open(audio_path, 'wb') as f:
                    f.write(wav_header)
                    f.write(audio_data)
                
                print(f"音频已保存到: {os.path.abspath(audio_path)}")
                self.audio_data = audio_data
                self.audio_response_received = True
                
            if not self.audio_response_received:
                print("警告: 未收到音频响应")
                return False
                
            return True
            
        except Exception as e:
            print(f"发送/接收文本消息时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_audio_file(self) -> Optional[str]:
        """保存音频数据到临时文件"""
        if not self.audio_data:
            print("没有可保存的音频数据")
            return None
            
        try:
            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            audio_path = os.path.join(temp_dir, "test_audio.wav")
            
            # 写入音频数据
            with open(audio_path, 'wb') as f:
                f.write(self.audio_data)
                
            self.audio_file = audio_path
            print(f"音频文件已保存到: {audio_path}")
            return audio_path
            
        except Exception as e:
            print(f"保存音频文件时出错: {e}")
            return None
    
    async def send_voice_message(self, voice_file_path: str) -> bool:
        """发送语音消息"""
        if not self.audio_params:
            print("Error: No audio parameters from server")
            return False
            
        try:
            # 发送开始录音消息
            start_listen_msg = {
                "type": "listen",
                "state": "start",
                "mode": "auto",
                "session_id": self.session_id,
                "version": 1,
                "audio_params": self.audio_params  # Use server's audio params
            }
            await self.websocket.send(json.dumps(start_listen_msg))
            self.logger.info("发送开始录音消息")

            # 读取语音文件并发送
            self.logger.info(f"读取语音文件: {voice_file_path}")
            with open(voice_file_path, "rb") as f:
                while True:
                    chunk = f.read(4096)  # Send in chunks
                    if not chunk:
                        break
                    await self.websocket.send(chunk)
                    self.logger.info(f"发送语音数据块: {len(chunk)} 字节")
                    await asyncio.sleep(0.01)  # Small delay between chunks

            # 发送结束录音消息
            stop_listen_msg = {
                "type": "listen",
                "state": "stop",
                "session_id": self.session_id,
                "version": 1
            }
            await self.websocket.send(json.dumps(stop_listen_msg))
            self.logger.info("发送结束录音消息")
            return True
        except Exception as e:
            self.logger.error(f"发送语音消息失败: {str(e)}")
            return False
    
    async def receive_responses(self):
        """接收并处理服务器响应"""
        try:
            last_activity = time.time()
            while time.time() - last_activity < self.timeout:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1)
                    last_activity = time.time()
                    
                    # 处理文本响应
                    if isinstance(message, str):
                        try:
                            response_data = json.loads(message)
                            msg_type = response_data.get('type')
                            
                            if msg_type == 'stt':
                                print(f"收到STT响应: {response_data.get('text', '')}")
                                self.text_response_received = True
                            elif msg_type == 'tts':
                                # Handle TTS state messages
                                state = response_data.get('state')
                                if state == 'sentence_start':
                                    print(f"TTS开始: {response_data.get('text', '')}")
                                elif state == 'sentence_end':
                                    print(f"TTS结束: {response_data.get('text', '')}")
                                elif state == 'stop':
                                    print("收到TTS结束消息")
                                    self.tts_stop_received = True
                                
                                # Check for base64 audio in TTS message
                                if 'audio' in response_data:
                                    audio_base64 = response_data['audio']
                                    try:
                                        audio_data = base64.b64decode(audio_base64)
                                        print(f"从Base64解码音频: {len(audio_data)} 字节")
                                        self.audio_data = audio_data
                                        self.audio_response_received = True
                                    except Exception as e:
                                        print(f"Base64音频解码失败: {e}")
                                
                            elif msg_type == 'error':
                                print(f"错误响应: {response_data.get('message', '')}")
                                
                            print(f"详细响应: {json.dumps(response_data, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"非JSON文本响应: {message}")
                    
                    # 处理二进制音频响应
                    elif isinstance(message, bytes):
                        print(f"收到音频数据: {len(message)} 字节")
                        self.audio_data = message
                        self.audio_response_received = True
                        
                except asyncio.TimeoutError:
                    continue
                    
        except Exception as e:
            print(f"接收响应时出错: {e}")

    async def run_test(self) -> bool:
        """运行完整的测试流程"""
        try:
            # 1. 连接到 WebSocket 服务器
            if not await self.connect():
                return False
            
            try:
                # 2. 发送 hello 握手消息
                if not await self.send_hello():
                    return False
                
                # 3. 发送测试语音消息
                voice_file = "test_voice.wav"  # 使用Opus格式
                if not await self.send_voice_message(voice_file):
                    return False
                    
                # 接收服务器响应
                await self.receive_responses()
                
            except websockets.exceptions.ConnectionClosed as e:
                print(f"WebSocket 连接已关闭: {e}")
                return False
            
            
            # 6. 检查测试结果 - 更新成功条件
            test_passed = all([
                self.hello_received,
                self.text_response_received # Only require valid audio file
            ])
            
            if test_passed:
                print("\n✅ 测试通过: 服务运行正常")
            else:
                print("\n❌ 测试失败: 服务响应不完整")
                
                if not self.hello_received:
                    print("  - 未收到 hello 响应")
                if not self.text_response_received:
                    print("  - 未收到STT文本响应")
                if not audio_valid:
                    print("  - 未生成有效的音频文件")
            
            return test_passed
            
        except Exception as e:
            print(f"测试过程中出错: {e}")
            return False
        finally:
            # 清理资源
            if hasattr(self, 'websocket') and hasattr(self.websocket, 'close'):
                try:
                    if not self.websocket.closed:
                        await self.websocket.close()
                except:
                    pass
                
            # 删除临时音频文件
            if hasattr(self, 'audio_file') and self.audio_file and os.path.exists(self.audio_file):
                try:
                    os.remove(self.audio_file)
                    os.rmdir(os.path.dirname(self.audio_file))
                except:
                    pass


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WebSocket 服务健康检查工具')
    parser.add_argument('--url', type=str, default=DEFAULT_WS_URL,
                      help=f'WebSocket 服务器地址 (默认: {DEFAULT_WS_URL})')
    parser.add_argument('--message', type=str, default=TEST_MESSAGE,
                      help=f'要发送的测试消息 (默认: "{TEST_MESSAGE}")')
    parser.add_argument('--voice', type=str, default="test_voice.wav",
                      help=f'要发送的语音文件路径 (默认: "test_voice.wav")')
    parser.add_argument('--timeout', type=int, default=10,
                      help='超时时间（秒）(默认: 10)')
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()
    
    print("=" * 50)
    print("WebSocket 服务健康检查工具")
    print("=" * 50)
    print(f"服务器: {args.url}")
    print(f"测试语音: {args.voice}")
    print(f"超时: {args.timeout} 秒")
    print("-" * 50)
    
    tester = WebSocketTester(
        ws_url=args.url,
        test_message=args.message,  # 保留但不再使用
        timeout=args.timeout
    )
    
    success = await tester.run_test()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
