import asyncio
import sys
import signal
from config.settings import load_config
from core.websocket_server import WebSocketServer
from core.ota_server import SimpleOtaServer
from core.http_api_server import HttpApiServer
from core.utils.util import check_ffmpeg_installed
from config.logger import setup_logging
from core.utils.util import get_local_ip
from aioconsole import ainput
from core.utils.redis_client import RedisClient

TAG = __name__
logger = setup_logging()


async def wait_for_exit() -> None:
    """
    阻塞直到收到 Ctrl‑C / SIGTERM。
    - Unix: 使用 add_signal_handler
    - Windows: 依赖 KeyboardInterrupt
    """
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        # Windows：await一个永远pending的fut，
        # 让 KeyboardInterrupt 冒泡到 asyncio.run，以此消除遗留普通线程导致进程退出阻塞的问题
        try:
            await asyncio.Future()
        except KeyboardInterrupt:  # Ctrl‑C
            pass


async def monitor_stdin():
    """监控标准输入，消费回车键"""
    try:
        while True:
            try:
                # 【重要修复】添加超时机制避免在输入流异常时CPU占用
                await asyncio.wait_for(ainput(), timeout=60.0)  # 60秒超时
            except asyncio.TimeoutError:
                # 超时是正常的，继续等待
                continue
            except EOFError:
                # 输入流结束，退出监控
                print("输入流结束，停止监控标准输入")
                break
            except Exception as e:
                print(f"标准输入监控异常: {e}")
                await asyncio.sleep(1)  # 短暂延迟避免忙等待
    except asyncio.CancelledError:
        print("标准输入监控被取消")
        raise
    except Exception as e:
        print(f"标准输入监控致命错误: {e}")
        raise

def check_redis_server(config):
    """
    每个用户的记忆数据和token都存储在redis中，
    因此每次启动时都要检查redis服务是否连接成功，否则退出进程
    """
    redis_client = RedisClient(config)
    redis_client.check_redis_sever()

async def cleanup_resources():
    """Ctrl-C退出本程序时，清理应用程序资源"""
    try:
        # 清理豆包TTS WebSocket连接
        from core.providers.tts.doubao import TTSProvider
        await TTSProvider.close_all_connections()
        logger.bind(tag=TAG).info("TTS WebSocket连接已清理")
    except Exception as e:
        logger.bind(tag=TAG).warning(f"清理TTS WebSocket连接时出错: {e}")

async def main():
    check_ffmpeg_installed()
    config = load_config()
    check_redis_server(config)

    # 添加 stdin 监控任务
    stdin_task = asyncio.create_task(monitor_stdin())

    # 启动 WebSocket 服务器
    ws_server = WebSocketServer(config)
    ws_task = asyncio.create_task(ws_server.start())

    # 启动 HTTP API 服务器
    http_api_server = HttpApiServer(config, ws_server)
    http_api_task = asyncio.create_task(http_api_server.start())

    ota_task = None

    read_config_from_api = config.get("read_config_from_api", False)
    if not read_config_from_api:
        # 启动 Simple OTA 服务器
        ota_server = SimpleOtaServer(config)
        ota_task = asyncio.create_task(ota_server.start())

        logger.bind(tag=TAG).info(
            "OTA接口是\t\thttp://{}:{}/xiaozhi/ota/",
            get_local_ip(),
            config["server"]["ota_port"],
        )

    # 获取HTTP API配置，使用安全的默认值
    http_api_port = 8100
    server_config = config.get("server", {})
    if isinstance(server_config, dict):
        http_api_port = int(server_config.get("http_api_port", 8100))

    logger.bind(tag=TAG).info(
        "HTTP API接口是\thttp://{}:{}/api/v1/",
        get_local_ip(),
        http_api_port,
    )

    # 获取WebSocket配置，使用安全的默认值
    websocket_port = 8000
    server_config = config.get("server", {})
    if isinstance(server_config, dict):
        websocket_port = int(server_config.get("port", 8000))

    logger.bind(tag=TAG).info(
        "Websocket地址是\tws://{}:{}/xiaozhi/v1/",
        get_local_ip(),
        websocket_port,
    )

    logger.bind(tag=TAG).info(
        "=======上面的地址是websocket协议地址，请勿用浏览器访问======="
    )
    logger.bind(tag=TAG).info(
        "如想测试websocket请用谷歌浏览器打开test目录下的test_page.html"
    )
    logger.bind(tag=TAG).info(
        "=============================================================\n"
    )

    try:
        await wait_for_exit()  # 阻塞直到收到退出信号
    except asyncio.CancelledError:
        print("任务被取消，清理资源中...")
    finally:
        # 取消所有任务（关键修复点）
        stdin_task.cancel()
        ws_task.cancel()
        http_api_task.cancel()
        if ota_task:
            ota_task.cancel()

        # 等待任务终止（必须加超时）
        tasks_to_wait = [stdin_task, ws_task, http_api_task]
        if ota_task:
            tasks_to_wait.append(ota_task)

        await asyncio.wait(
            tasks_to_wait,
            timeout=3.0,
            return_when=asyncio.ALL_COMPLETED
        )
        # 清理应用程序资源
        await cleanup_resources()
        
        print("服务器已关闭，程序退出。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("手动中断，程序终止。")
    except Exception as e:
        # 确保使用原始stderr输出错误
        try:
            logger.bind(tag=TAG).error(f"程序异常退出: {e}")
        except:
            # 如果日志系统已经关闭，直接输出到stderr
            print(f"程序异常退出: {e}")
