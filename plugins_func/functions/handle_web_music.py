# In plugins_func/functions/handle_music.py

import json
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.logger import setup_logging

logger = setup_logging()
TAG = "handle_web_music"

# Define the schema for the LLM function call
play_music_url_function_desc = {
    "type": "function",
    "function": {
        "name": "play_music_url",
        "description": "当用户选择一首歌曲时调用，本函数收到该歌曲的URL后，会向客户端发送播放音乐的指令。",
        "parameters": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string",
                    "description": "播放音乐的网页URL"
                }
            },
            "required": ["url"]
        }
    }
}

# Register the function
@register_function('play_music_url', play_music_url_function_desc, ToolType.SYSTEM_CTL)
def play_music_url(conn, url: str):
    """
    Sends a command to the client to play music from the given URL.
    'conn' is the ConnectionHandler instance.
    'url' is the music URL provided by the LLM.
    """
    if not conn.websocket:
        logger.bind(tag=TAG).warning("WebSocket connection not available for play_music_url.")
        # This response goes back to the LLM, which then formulates a user-facing message.
        return ActionResponse(action=Action.RESPONSE, result=None, response="客户端未连接，放弃播放")
    playback_command = {
        "type": "music",  # As per your requirement
        "url": url
    }
    try:
        msg = json.dumps(playback_command)
        logger.bind(tag=TAG).info(f"发送音乐播放指令: {msg}")
        conn.websocket.send(msg)
        # This response tells the LLM the action was successful.
        # The LLM will then generate a natural language response for the user, e.g., "好的，正在为您播放第三首歌。"
        return ActionResponse(action=Action.RESPONSE, result={"status": "success", "message": f"Playback command sent for {url}"}, response="播放音乐的功能即将上线，敬请期待！")
    except Exception as e:
        logger.bind(tag=TAG).error(f"Error sending music playback command via WebSocket: {e}")
        return ActionResponse(action=Action.RESPONSE, result={"status": "error", "message": str(e)}, response=f"哎呀，播放音乐时出错了，我需要花点时间解决这个问题，要不我们先聊点别的吧")