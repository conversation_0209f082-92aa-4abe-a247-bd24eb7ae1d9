from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.logger import setup_logging
import json
import time

TAG = __name__
logger = setup_logging()

set_user_name_function_desc = {
    "type": "function",
    "function": {
        "name": "set_user_name",
        "description": "当用户告知AI助手自己的名字或希望被如何称呼时，调用此函数，例如，用户说'叫我XX'、'我叫XX'、'我的名字是XX'、'我是XX'等。",
        "parameters": {
            "type": "object",
            "properties": {
                "user_name": {
                    "type": "string",
                    "description": "从用户消息中提取的具体称呼名字"
                }
            },
            "required": ["user_name"]
        }
    }
}

@register_function('set_user_name', set_user_name_function_desc, ToolType.CHANGE_SYS_PROMPT)
def set_user_name(conn, user_name: str):
    """
    设置用户姓名/称呼
    
    Args:
        conn: 连接对象
        user_name: 用户姓名
    """
    logger.bind(tag=TAG).info(f"🎯 set_user_name函数被调用，参数: user_name='{user_name}'")
    try:
        # 验证用户名
        if not user_name or not user_name.strip():
            error_msg = "没有听出用户的名字"
            logger.bind(tag=TAG).warning(f"用户名解析为空")
            return ActionResponse(action=Action.RESPONSE, result=error_msg, response="不好意思，我还没有听清楚你的名字呢，能再说一遍吗？")
        
        user_name = user_name.strip()
        
        # 获取当前状态
        old_name = None
        current_role = None
        
        # 优先使用用户配置管理器的方法
        if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
            old_name = conn.user_profile_manager.get_user_name()
            current_role = conn.user_profile_manager.current_role

            # 检查是否真的需要更改
            if user_name == old_name:
                return ActionResponse(action=Action.RESPONSE, result="用户名未变化", response=f"是呀，你就是{user_name}呀！")

            # 使用用户配置管理器的方法设置用户名（会自动保存到Redis）
            success = conn.user_profile_manager.set_user_name(user_name)
            if not success:
                logger.bind(tag=TAG).error(f"通过用户配置管理器设置用户名失败")
                return ActionResponse(action=Action.RESPONSE, result="设置用户名失败", response="抱歉，设置用户名时出现了问题")
        else:
            # 兼容性处理：如果没有用户配置管理器，使用旧方法
            old_name = getattr(conn, 'user_name', '')
            current_role = getattr(conn, 'current_role', None)
            
            # 检查是否真的需要更改
            if user_name == old_name:
                return ActionResponse(action=Action.RESPONSE, result="用户名未变化", response=f"是呀，你就是{user_name}呀！")
            
            # 更新连接处理器的状态
            conn.user_name = user_name
            
            # 保存用户设置到Redis（兼容性方法）
            try:
                if hasattr(conn, '_save_user_settings'):
                    conn._save_user_settings()
                else:
                    logger.bind(tag=TAG).warning(f"连接对象没有_save_user_settings方法")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"保存用户设置失败: {e}")
        
        # 立即保存到记忆系统
        if hasattr(conn, 'memory') and conn.memory:
            try:
                _save_user_name_to_memory(conn, user_name)
            except Exception as e:
                logger.bind(tag=TAG).error(f"保存用户名到记忆系统失败: {e}")
        
        # 记录日志
        if old_name:
            logger.bind(tag=TAG).info(f"用户名更新成功: {old_name} -> {user_name}")
        else:
            logger.bind(tag=TAG).info(f"用户名设置成功: {user_name}")
        
        # 生成个性化回复
        response = _generate_name_response(user_name, current_role, old_name)
        
        return ActionResponse(action=Action.RESPONSE, result="设置用户名成功", response=response)
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"设置用户名失败: {e}")
        return ActionResponse(action=Action.RESPONSE, result="设置用户名失败", response="抱歉，设置用户名时出现了问题")

def _generate_name_response(user_name: str, current_role: str, old_name: str) -> str:
    """生成设置用户名的个性化回复"""
    response = f"好的，{user_name}！我记住了。"        
    
    if old_name:
        return f"明白了！{response.replace(user_name, f'{old_name}改成{user_name}')}"
    
    return response

def _save_user_name_to_memory(conn, user_name: str):
    """立即将用户名保存到记忆系统，复用现有的记忆管理方法"""
    try:
        # 优先使用记忆系统的专门方法（如果存在）
        if hasattr(conn.memory, 'save_assistant_name'):
            # 复用 set_assistant_name.py 中的记忆更新逻辑
            try:
                # 直接调用记忆系统的用户名保存方法（如果存在）
                if hasattr(conn.memory, 'save_user_name'):
                    conn.memory.save_user_name(user_name)
                    logger.bind(tag=TAG).info(f"✅ 通过记忆系统专用方法保存用户名: {user_name}")
                    return
            except Exception as e:
                logger.bind(tag=TAG).warning(f"记忆系统专用方法失败: {e}")
        
        # 复用 set_assistant_name.py 中的记忆更新逻辑
        if hasattr(conn.memory, 'short_momery'):
            try:
                # 调用类似 set_assistant_name.py 中的 _update_memory_assistant_name_sync 方法
                _update_memory_user_name_sync(conn, user_name)
                logger.bind(tag=TAG).info(f"✅ 通过记忆同步更新方法保存用户名: {user_name}")
                return
            except Exception as e:
                logger.bind(tag=TAG).warning(f"记忆同步更新失败: {e}")
        
        # 如果以上方法都不可用，记录警告
        logger.bind(tag=TAG).warning("记忆系统不支持用户名保存，跳过记忆更新")
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"保存用户名到记忆系统失败: {e}")
        raise

def _update_memory_user_name_sync(conn, new_name: str):
    """同步更新记忆系统中的用户名信息，复用 set_assistant_name.py 的逻辑"""
    # 获取当前记忆内容
    current_memory = conn.memory.short_momery
    if not current_memory:
        logger.bind(tag=TAG).debug("记忆为空，创建新的用户名记录")
        # 创建最简单的记忆结构
        memory_data = {
            "user_profile": {"name": new_name},
            "conversation_context": {"recent_topics": [f"用户自我介绍：{new_name}"], "ongoing_matters": []},
            "preferences": {}
        }
        conn.memory.short_momery = json.dumps(memory_data, ensure_ascii=False)
        conn.memory.save_memory_to_file()
        return

    try:
        # 解析记忆JSON
        memory_data = json.loads(current_memory)

        # 确保记忆数据结构存在
        if "user_profile" not in memory_data:
            memory_data["user_profile"] = {}
        if "conversation_context" not in memory_data:
            memory_data["conversation_context"] = {"recent_topics": [], "ongoing_matters": []}

        # 获取用户档案
        user_profile = memory_data["user_profile"]

        # 检查是否有用户名相关信息需要更新
        old_user_name = user_profile.get("name", "")

        # 如果有旧的用户名且与新名字不同，进行更新
        if old_user_name and old_user_name != new_name:
            # 记录名字变更到 important_dates
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            name_change_note = f"用户名更新：{old_user_name} -> {new_name} ({time_str})"

            # 将变更记录添加到重要日期
            if "important_dates" not in user_profile:
                user_profile["important_dates"] = ""

            if user_profile["important_dates"]:
                user_profile["important_dates"] += f"; {name_change_note}"
            else:
                user_profile["important_dates"] = name_change_note

            # 添加到最近话题中，让AI知道名字发生了变化
            recent_topics = memory_data["conversation_context"]["recent_topics"]
            name_change_topic = f"用户名更新为{new_name}"
            if name_change_topic not in recent_topics:
                recent_topics.insert(0, name_change_topic)  # 插入到最前面
                # 保持recent_topics不超过5个
                if len(recent_topics) > 5:
                    recent_topics = recent_topics[:5]
                memory_data["conversation_context"]["recent_topics"] = recent_topics

            logger.bind(tag=TAG).info(f"已更新记忆系统中的用户名: {old_user_name} -> {new_name}")
        else:
            # 即使没有旧名字，也要确保新名字被记录
            logger.bind(tag=TAG).debug(f"设置记忆中的用户名: {new_name}")

        # 更新用户名
        user_profile["name"] = new_name

        # 更新记忆内容
        updated_memory = json.dumps(memory_data, ensure_ascii=False)
        conn.memory.short_momery = updated_memory

        # 保存更新后的记忆
        conn.memory.save_memory_to_file()

        logger.bind(tag=TAG).info(f"记忆系统中的用户名已更新为: {new_name}")

    except json.JSONDecodeError as e:
        logger.bind(tag=TAG).warning(f"记忆内容不是有效的JSON格式，无法更新用户名: {e}")
        # 如果JSON解析失败，创建新的记忆结构
        try:
            memory_data = {
                "user_profile": {"name": new_name},
                "conversation_context": {"recent_topics": [f"用户自我介绍：{new_name}"], "ongoing_matters": []},
                "preferences": {}
            }
            conn.memory.short_momery = json.dumps(memory_data, ensure_ascii=False)
            conn.memory.save_memory_to_file()
            logger.bind(tag=TAG).info(f"已创建新的记忆结构并保存用户名: {new_name}")
        except Exception as fallback_error:
            logger.bind(tag=TAG).error(f"创建新记忆结构也失败: {fallback_error}")
    except Exception as e:
        logger.bind(tag=TAG).error(f"更新记忆中的用户名时出错: {e}")
        raise