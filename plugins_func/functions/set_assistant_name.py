from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.logger import setup_logging
import json
import time

TAG = __name__
logger = setup_logging()

set_assistant_name_function_desc = {
    "type": "function",
    "function": {
        "name": "set_assistant_name",
        "description": "当用户给AI助手起名字或者修改AI助手名字时调用，如'给你起个名字叫XX'、'从现在开始你就叫XX'等。",
        "parameters": {
            "type": "object",
            "properties": {
                "assistant_name": {
                    "type": "string",
                    "description": "要设置的助手名字"
                }
            },
            "required": ["assistant_name"]
        }
    }
}

@register_function('set_assistant_name', set_assistant_name_function_desc, ToolType.CHANGE_SYS_PROMPT)
def set_assistant_name(conn, assistant_name: str):
    """
    设置助手名字
    
    Args:
        conn: 连接对象
        assistant_name: 助手名字
    """
    try:
        # 验证助手名字
        if not assistant_name or not assistant_name.strip():
            error_msg = "助手名字不能为空"
            logger.bind(tag=TAG).warning(f"助手名字解析为空")
            return ActionResponse(action=Action.RESPONSE, result=error_msg, response="不好意思，我还没有听清楚你要给我起什么名字呢，能再说一遍吗？")
        
        assistant_name = assistant_name.strip()
        
        # 获取当前状态
        old_name = None
        current_role = None
        
        # 优先使用用户配置管理器的方法
        if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
            old_name = conn.user_profile_manager.get_assistant_name()
            current_role = conn.user_profile_manager.current_role

            # 检查是否真的需要更改
            if assistant_name == old_name:
                return ActionResponse(action=Action.RESPONSE, result="名字未变化", response=f"我本来就叫{assistant_name}呀！")

            # 使用用户配置管理器的方法设置助手名字（会自动保存到Redis）
            success = conn.user_profile_manager.set_assistant_name(assistant_name)
            if not success:
                logger.bind(tag=TAG).error(f"通过用户配置管理器设置助手名字失败")
                return ActionResponse(action=Action.RESPONSE, result="设置名字失败", response="抱歉，设置名字时出现了问题")
        else:
            # 兼容性处理：如果没有用户配置管理器，使用旧方法
            old_name = getattr(conn, 'assistant_name', '语嫣')
            current_role = getattr(conn, 'current_role', None)
            
            # 检查是否真的需要更改
            if assistant_name == old_name:
                return ActionResponse(action=Action.RESPONSE, result="名字未变化", response=f"我本来就叫{assistant_name}呀！")
            
            # 更新连接处理器的状态
            conn.assistant_name = assistant_name
            
            # 保存用户设置到Redis（兼容性方法）
            try:
                if hasattr(conn, '_save_user_settings'):
                    conn._save_user_settings()
                else:
                    logger.bind(tag=TAG).warning(f"连接对象没有_save_user_settings方法")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"保存用户设置失败: {e}")
        
        # 应用角色配置（更新提示词中的名字）
        if hasattr(conn, '_apply_role_config'):
            try:
                conn._apply_role_config()
            except Exception as e:
                logger.bind(tag=TAG).warning(f"应用角色配置失败: {e}")
        
        # 如果有当前角色，需要更新系统提示词中的名字
        if current_role:
            try:
                from config.role_loader import get_role_loader
                role_loader = get_role_loader()
                role_config = role_loader.get_role_config(current_role)
                if role_config:
                    new_prompt = role_config["prompt"].replace("{{assistant_name}}", assistant_name)
                    if hasattr(conn, 'change_system_prompt'):
                        conn.change_system_prompt(new_prompt)
            except Exception as e:
                logger.bind(tag=TAG).warning(f"更新系统提示词失败: {e}")
        
        # 更新记忆系统中的相关信息
        if hasattr(conn, 'memory') and conn.memory:
            try:
                _update_memory_assistant_name_sync(conn, assistant_name)
            except Exception as e:
                logger.bind(tag=TAG).error(f"更新记忆系统中的助手名字失败: {e}")
        
        # 记录日志
        logger.bind(tag=TAG).info(f"助手名字更新成功: {old_name} -> {assistant_name}")
        
        # 生成个性化回复
        response = _generate_name_response(assistant_name, current_role)
        
        return ActionResponse(action=Action.RESPONSE, result="设置名字成功", response=response)
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"设置助手名字失败: {e}")
        return ActionResponse(action=Action.RESPONSE, result="设置名字失败", response="哎呀，我这里记名字出了点问题，要不我们先聊点别的吧")

def _generate_name_response(assistant_name, current_role):
    """生成设置名字的个性化回复"""
    return f"从现在开始我就是{assistant_name}啦～"

def _update_memory_assistant_name_sync(conn, new_name):
    """同步更新记忆系统中的助手名字信息"""
    # 获取当前记忆内容
    current_memory = conn.memory.short_momery
    if not current_memory:
        logger.bind(tag=TAG).debug("记忆为空，无需更新助手名字")
        return

    try:
        # 解析记忆JSON
        memory_data = json.loads(current_memory)

        # 确保记忆数据结构存在
        if "user_profile" not in memory_data:
            memory_data["user_profile"] = {}
        if "conversation_context" not in memory_data:
            memory_data["conversation_context"] = {"recent_topics": [], "ongoing_matters": []}

        # 获取用户档案
        user_profile = memory_data["user_profile"]

        # 检查是否有助手名字相关信息需要更新
        old_assistant_name = user_profile.get("assistant_name", "")

        # 如果有旧的助手名字且与新名字不同，进行更新
        if old_assistant_name and old_assistant_name != new_name:
            # 记录名字变更到 important_dates 或创建专门的变更记录
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            name_change_note = f"助手名字更新：{old_assistant_name} -> {new_name} ({time_str})"

            # 将变更记录添加到重要日期或创建变更历史
            if "important_dates" not in user_profile:
                user_profile["important_dates"] = ""

            if user_profile["important_dates"]:
                user_profile["important_dates"] += f"; {name_change_note}"
            else:
                user_profile["important_dates"] = name_change_note

            # 添加到最近话题中，让AI知道名字发生了变化
            recent_topics = memory_data["conversation_context"]["recent_topics"]
            name_change_topic = f"助手改名为{new_name}"
            if name_change_topic not in recent_topics:
                recent_topics.insert(0, name_change_topic)  # 插入到最前面
                # 保持recent_topics不超过5个
                if len(recent_topics) > 5:
                    recent_topics = recent_topics[:5]
                memory_data["conversation_context"]["recent_topics"] = recent_topics

            logger.bind(tag=TAG).info(f"已更新记忆系统中的助手名字: {old_assistant_name} -> {new_name}")
        else:
            # 即使没有旧名字，也要确保新名字被记录
            logger.bind(tag=TAG).debug(f"设置记忆中的助手名字: {new_name}")

        # 更新助手名字
        user_profile["assistant_name"] = new_name

        # 更新记忆内容
        updated_memory = json.dumps(memory_data, ensure_ascii=False)
        conn.memory.short_momery = updated_memory

        # 保存更新后的记忆
        conn.memory.save_memory_to_file()

        # 同时更新独立的助手名字存储
        conn.memory.save_assistant_name(new_name)

        logger.bind(tag=TAG).info(f"记忆系统中的助手名字已更新为: {new_name}")

    except json.JSONDecodeError as e:
        logger.bind(tag=TAG).warning(f"记忆内容不是有效的JSON格式，无法更新助手名字: {e}")
        # 如果JSON解析失败，至少更新独立的助手名字存储
        try:
            conn.memory.save_assistant_name(new_name)
            logger.bind(tag=TAG).info(f"已通过独立存储更新助手名字: {new_name}")
        except Exception as fallback_error:
            logger.bind(tag=TAG).error(f"独立存储助手名字也失败: {fallback_error}")
    except Exception as e:
        logger.bind(tag=TAG).error(f"更新记忆中的助手名字时出错: {e}")
        # 如果更新记忆失败，至少尝试更新独立的助手名字存储
        try:
            conn.memory.save_assistant_name(new_name)
            logger.bind(tag=TAG).info(f"已通过独立存储更新助手名字: {new_name}")
        except Exception as fallback_error:
            logger.bind(tag=TAG).error(f"独立存储助手名字也失败: {fallback_error}")
