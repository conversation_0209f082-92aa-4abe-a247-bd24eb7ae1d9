from plugins_func.register import register_function, ToolType, ActionResponse, Action

plugin_loader_function_desc = {
    "type": "function",
    "function": {
        "name": "plugin_loader",
        "description": "当用户想加载或卸载插件/function时，调用此函数：支持的插件列表为[plugins]",
        "parameters": {
            "type": "object",
            "properties": {
                "oper": {"type": "string", "description": "load or unload"},
                "name": {"type": "string", "description": "要加载或卸载的插件名字"},
            },
            "required": ["oper", "name"],
        },
    },
}


@register_function("plugin_loader", plugin_loader_function_desc, ToolType.SYSTEM_CTL)
def plugin_loader(conn, oper: str, name: str):
    """插件加载"""
    if oper not in ["load", "unload"]:
        conn.logger.bind(tag="plugin_loader").warning(f"不支持的操作: {oper}")
        return ActionResponse(
            action=Action.NONE, result="插件操作失败", response=""
        )

    cur_support = conn.func_handler.current_support_functions()
    if oper == "load":
        if name in cur_support:
            conn.logger.bind(tag="plugin_loader").info(f"{name}插件已加载,无需重复加载")
            return ActionResponse(
                action=Action.REQLLM,
                result=f"{name}插件已准备就绪，可以使用",
                response=f"{name}插件已加载,无需重复加载",
            )
        func = conn.func_handler.function_registry.register_function(name)
        if not func:
            conn.logger.bind(tag="plugin_loader").warning(f"插件未找到: {name}")
            return ActionResponse(
                action=Action.REQLLM, result=f"插件{name}未找到，无法加载", response="插件未找到"
            )
        conn.logger.bind(tag="plugin_loader").info(f"{name}插件加载成功")
        conn.func_handler.upload_functions_desc()
        return ActionResponse(
            action=Action.REQLLM,
            result=f"{name}插件加载成功，现在可以使用",
            response=f"{name}插件加载成功"
        )
    else:
        if name not in cur_support:
            conn.logger.bind(tag="plugin_loader").info(f"{name}插件未加载")
            return ActionResponse(
                action=Action.REQLLM,
                result=f"{name}插件未加载，无需卸载",
                response=f"{name}插件未加载",
            )
        bOK = conn.func_handler.function_registry.unregister_function(name)
        if not bOK:
            conn.logger.bind(tag="plugin_loader").warning(f"插件卸载失败: {name}")
            return ActionResponse(
                action=Action.REQLLM, result=f"插件{name}卸载失败", response=""
            )
        conn.logger.bind(tag="plugin_loader").info(f"{name}插件卸载成功")
        conn.func_handler.upload_functions_desc()
        return ActionResponse(
            action=Action.REQLLM,
            result=f"{name}插件卸载成功",
            response=f"{name}插件卸载成功"
        )
