from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.logger import setup_logging
from config.role_loader import get_role_loader

TAG = __name__
logger = setup_logging()

# 获取角色加载器实例
role_loader = get_role_loader()

def get_switch_role_function_desc():
    """动态生成function描述，包含当前可用的角色列表"""
    available_roles = role_loader.get_available_roles()
    roles_str = ",".join(available_roles)
    
    return {
        "type": "function",
        "function": {
            "name": "switch_role",
            "description": f"当用户发出切换角色或者AI声音的指令时调用，如'切换到XX'、'换个声音'、'换个角色'等。可用角色：[{roles_str}]。",
            "parameters": {
                "type": "object",
                "properties": {
                    "role_personality": {
                        "type": "string",
                        "description": "要切换的角色风格和声音。当用户说出任何角色名称或别名时，必须传入此参数。"
                    }
                },
                "required": ["role_personality"]
            }
        }
    }

# 注册函数时使用动态生成的描述
switch_role_function_desc = get_switch_role_function_desc()

@register_function('switch_role', switch_role_function_desc, ToolType.CHANGE_SYS_PROMPT)
def switch_role(conn, role_personality: str):
    """
    切换助手角色
    
    Args:
        conn: 连接对象
        role_personality: 角色性格类型
    """
    try:
        # 获取当前状态
        old_role = getattr(conn, 'current_role', None)
        assistant_name = getattr(conn, 'assistant_name', '语嫣')
        
        # 标准化角色名称（支持别名）
        normalized_role = role_loader.normalize_role_name(role_personality)
        
        if not normalized_role:
            available_roles = role_loader.get_available_roles()
            error_msg = f"不支持的角色性格。可选择的角色性格有：{', '.join(available_roles)}"
            logger.bind(tag=TAG).warning(f"角色切换失败: {error_msg}")
            return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response=error_msg)
        
        # 检查是否真的需要切换
        if normalized_role == old_role:
            return ActionResponse(action=Action.RESPONSE, result="角色未变化", response="我本来就是这个角色呀！")
        
        # 获取角色配置
        role_config = role_loader.get_role_config(normalized_role)
        if not role_config:
            error_msg = f"获取角色配置失败: {normalized_role}"
            logger.bind(tag=TAG).error(error_msg)
            return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response="角色配置加载失败")
        
        # 优先使用用户配置管理器的方法
        if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
            conn.user_profile_manager.current_role = normalized_role
            # 保存设置到Redis（会自动保存角色变化）
            conn.user_profile_manager.save_user_settings()
        else:
            # 兼容性处理：如果没有用户配置管理器，使用旧方法
            conn.current_role = normalized_role
            # 保存用户设置到Redis
            if hasattr(conn, '_save_user_settings'):
                conn._save_user_settings()
        
        # 应用角色配置
        if hasattr(conn, '_apply_role_config'):
            conn._apply_role_config()
        
        # 更新系统提示词
        new_prompt = role_config["prompt"].replace("{{assistant_name}}", assistant_name)
        if hasattr(conn, 'change_system_prompt'):
            conn.change_system_prompt(new_prompt)
        
        # 更新TTS声音 - 使用用户专属配置避免共享实例冲突
        voice = role_config.get("voice")
        if voice:
            # 存储在连接级别的用户专属TTS配置中，而不是修改共享TTS实例
            if not hasattr(conn, '_user_tts_config'):
                conn._user_tts_config = {}
            old_voice = conn._user_tts_config.get('voice', 'default')
            conn._user_tts_config['voice'] = voice
            
            # 也检查是否有其他TTS参数需要更新
            for param in ['speed_ratio', 'volume_ratio', 'pitch_ratio']:
                if param in role_config:
                    conn._user_tts_config[param] = role_config[param]
                    logger.bind(tag=TAG).info(f"[角色切换] 更新TTS参数 {param}: {role_config[param]}")
            
            logger.bind(tag=TAG).info(f"[角色切换] TTS声音配置更新: client_id={getattr(conn, 'client_id', 'unknown')}, 角色='{normalized_role}', 从'{old_voice}'更改为'{voice}'")
            logger.bind(tag=TAG).info(f"[角色切换] 用户专属TTS配置已更新，避免共享实例冲突")
            logger.bind(tag=TAG).info(f"[角色切换] 完整TTS配置: {conn._user_tts_config}")
        
        # 记录日志
        logger.bind(tag=TAG).info(f"角色切换成功: {old_role} -> {normalized_role}")
        if voice:
            logger.bind(tag=TAG).info(f"声音切换为: {voice}")
        
        # 生成个性化回复
        response = _generate_switch_response(normalized_role, assistant_name)
        
        return ActionResponse(action=Action.RESPONSE, result="切换角色成功", response=response)
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"切换角色失败: {e}")
        return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response="抱歉，切换角色时出现了问题")

def _generate_switch_response(normalized_role, assistant_name):
    """生成角色切换的个性化回复"""
    personality_responses = {
        "俏皮女闺蜜": f"哈哈哈，切换成功啦！我是你的俏皮闺蜜{assistant_name}，准备好和我一起嗨起来了吗？😄",
        "成熟女闺蜜": f"切换角色成功。我是你的成熟系闺蜜{assistant_name}。很高兴能以这种方式陪伴你，有什么想聊的吗？",
        "治愈男闺蜜": f"切换成功啦～我是你的治愈系闺蜜{assistant_name}呢，会温柔地陪伴在你身边哦💕"
    }
    return personality_responses.get(normalized_role, f"切换角色成功，我是{normalized_role}性格的闺蜜{assistant_name}")

def reload_role_config():
    """重新加载角色配置（用于热更新）"""
    global switch_role_function_desc
    role_loader.reload_config()
    switch_role_function_desc = get_switch_role_function_desc()
    logger.bind(tag=TAG).info("角色配置已重新加载")
