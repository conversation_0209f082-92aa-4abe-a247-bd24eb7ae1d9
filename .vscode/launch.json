{"version": "0.2.0", "configurations": [{"name": "Python: app.py", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/", "python": "${workspaceFolder}/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/.venv/lib/python3.12/site-packages", "PATH": "/opt/homebrew/opt/ffmpeg@6/bin:${env:PATH}", "DYLD_LIBRARY_PATH": "/opt/homebrew/opt/ffmpeg@6/lib:${env:DYLD_LIBRARY_PATH}"}, "justMyCode": true}]}